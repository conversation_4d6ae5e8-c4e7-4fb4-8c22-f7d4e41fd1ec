import asyncio
from playwright.async_api import async_playwright

async def simple_copy_operation():
    """简单的复制操作 - 连接到现有浏览器"""
    print('🔗 连接到现有浏览器...')
    
    async with async_playwright() as p:
        # 连接到现有的浏览器实例
        try:
            # 启动新的浏览器实例
            browser = await p.chromium.launch(headless=False, slow_mo=1000)
            context = await browser.new_context()
            page = await context.new_page()
            
            # 导航到页面
            await page.goto('https://textbookeditor.lessonplan.cn/ed0b4d38-03c8-150b-edab-a4d423944978')
            print('✅ 已连接到页面')
            
            # 等待用户手动登录
            print('⏳ 请在浏览器中手动完成登录，然后按回车继续...')
            input('按回车键继续...')
            
            # 等待页面加载
            await page.wait_for_timeout(3000)
            
            print('🔍 开始查找页面内容...')
            
            # 获取页面标题
            title = await page.title()
            print(f'📄 页面标题: {title}')
            
            # 获取页面URL
            url = page.url
            print(f'🌐 当前URL: {url}')
            
            # 截图
            await page.screenshot(path='manual_login_page.png', full_page=True)
            print('📸 已保存页面截图: manual_login_page.png')
            
            # 查找所有可能的内容
            print('🔍 查找页面中的所有文本内容...')
            
            # 尝试查找包含"1.1"的元素
            elements_with_11 = await page.query_selector_all('*')
            found_elements = []
            
            for i, element in enumerate(elements_with_11[:100]):  # 只检查前100个元素
                try:
                    text = await element.text_content()
                    if text and '1.1' in text:
                        found_elements.append((element, text))
                        print(f'📝 找到包含1.1的元素 {len(found_elements)}: {text[:80]}...')
                except:
                    continue
            
            if found_elements:
                print(f'✅ 总共找到 {len(found_elements)} 个包含1.1的元素')
                
                # 选择第一个元素进行操作
                target_element, target_text = found_elements[0]
                print(f'🎯 选择第一个元素进行复制: {target_text[:50]}...')
                
                # 点击元素
                await target_element.click()
                await page.wait_for_timeout(1000)
                
                # 全选并复制
                await page.keyboard.press('Control+A')
                await page.wait_for_timeout(500)
                await page.keyboard.press('Control+C')
                await page.wait_for_timeout(500)
                print('✅ 已复制内容')
                
                # 移动到末尾并粘贴
                await page.keyboard.press('Control+End')
                await page.keyboard.press('Enter')
                await page.keyboard.press('Enter')
                await page.wait_for_timeout(500)
                
                await page.keyboard.press('Control+V')
                await page.wait_for_timeout(2000)
                print('✅ 已粘贴内容')
                
                # 尝试修改新粘贴的内容
                new_elements = await page.query_selector_all('*')
                new_found = []
                
                for element in new_elements:
                    try:
                        text = await element.text_content()
                        if text and '1.1' in text:
                            new_found.append((element, text))
                    except:
                        continue
                
                if len(new_found) > len(found_elements):
                    print('✅ 检测到新的1.1元素，尝试修改为1.2')
                    
                    # 获取最后一个新元素
                    last_element = new_found[-1][0]
                    await last_element.click()
                    await page.wait_for_timeout(500)
                    
                    # 尝试替换文本
                    await page.keyboard.press('Control+A')
                    await page.keyboard.type('1.2')
                    await page.wait_for_timeout(500)
                    
                    print('✅ 已修改为1.2节')
                else:
                    print('❌ 未检测到新的复制内容')
                
            else:
                print('❌ 未找到包含1.1的元素')
                
                # 尝试查找iframe
                iframes = await page.query_selector_all('iframe')
                print(f'🔍 找到 {len(iframes)} 个iframe')
                
                for i, iframe in enumerate(iframes):
                    try:
                        frame = await iframe.content_frame()
                        if frame:
                            print(f'🔍 检查第 {i+1} 个iframe...')
                            
                            frame_elements = await frame.query_selector_all('*')
                            for element in frame_elements[:50]:
                                try:
                                    text = await element.text_content()
                                    if text and '1.1' in text:
                                        print(f'📝 在iframe中找到: {text[:50]}...')
                                        
                                        # 在iframe中执行复制操作
                                        await element.click()
                                        await frame.wait_for_timeout(1000)
                                        
                                        await frame.keyboard.press('Control+A')
                                        await frame.wait_for_timeout(500)
                                        await frame.keyboard.press('Control+C')
                                        await frame.wait_for_timeout(500)
                                        
                                        await frame.keyboard.press('Control+End')
                                        await frame.keyboard.press('Enter')
                                        await frame.keyboard.press('Enter')
                                        await frame.wait_for_timeout(500)
                                        
                                        await frame.keyboard.press('Control+V')
                                        await frame.wait_for_timeout(2000)
                                        
                                        print('✅ 在iframe中完成复制粘贴操作')
                                        break
                                except:
                                    continue
                    except Exception as e:
                        print(f'❌ 处理iframe时出错: {e}')
            
            print('🎉 操作完成！')
            print('📝 请检查页面内容并手动调整')
            print('💡 使用 Ctrl+M 渲染公式')
            
            # 保持浏览器打开
            print('🌐 浏览器保持打开状态...')
            await page.wait_for_timeout(300000)  # 等待5分钟
            
        except Exception as e:
            print(f'❌ 操作过程中出错: {e}')

if __name__ == "__main__":
    asyncio.run(simple_copy_operation())
