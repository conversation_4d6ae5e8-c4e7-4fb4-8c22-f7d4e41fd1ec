<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1707.84pt" height="987.84pt" viewBox="0 0 1707.84 987.84" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:59:46.822359</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 987.84 
L 1707.84 987.84 
L 1707.84 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 77.76 299.232 
L 501.12 299.232 
Q 515.232 299.232 515.232 291.12 
L 515.232 88.32 
Q 515.232 80.208 501.12 80.208 
L 77.76 80.208 
Q 63.648 80.208 63.648 88.32 
L 63.648 291.12 
Q 63.648 299.232 77.76 299.232 
z
" clip-path="url(#p64a9a70894)" style="fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_3">
    <path d="M 571.68 299.232 
L 1347.84 299.232 
Q 1361.952 299.232 1361.952 291.12 
L 1361.952 88.32 
Q 1361.952 80.208 1347.84 80.208 
L 571.68 80.208 
Q 557.568 80.208 557.568 88.32 
L 557.568 291.12 
Q 557.568 299.232 571.68 299.232 
z
" clip-path="url(#p64a9a70894)" style="fill: #fff3e0; stroke: #f57c00; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 1418.4 299.232 
L 1630.08 299.232 
Q 1644.192 299.232 1644.192 291.12 
L 1644.192 88.32 
Q 1644.192 80.208 1630.08 80.208 
L 1418.4 80.208 
Q 1404.288 80.208 1404.288 88.32 
L 1404.288 291.12 
Q 1404.288 299.232 1418.4 299.232 
z
" clip-path="url(#p64a9a70894)" style="fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 571.68 380.352 
L 1347.84 380.352 
Q 1361.952 380.352 1361.952 372.24 
L 1361.952 307.344 
Q 1361.952 299.232 1347.84 299.232 
L 571.68 299.232 
Q 557.568 299.232 557.568 307.344 
L 557.568 372.24 
Q 557.568 380.352 571.68 380.352 
z
" clip-path="url(#p64a9a70894)" style="fill: #fff3e0; stroke: #f57c00; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_6">
    <path d="M 571.68 461.472 
L 1347.84 461.472 
Q 1361.952 461.472 1361.952 453.36 
L 1361.952 388.464 
Q 1361.952 380.352 1347.84 380.352 
L 571.68 380.352 
Q 557.568 380.352 557.568 388.464 
L 557.568 453.36 
Q 557.568 461.472 571.68 461.472 
z
" clip-path="url(#p64a9a70894)" style="fill: #ffebee; stroke: #d32f2f; stroke-width: 3; stroke-linejoin: miter"/>
   </g>
   <g id="patch_7">
    <path d="M 176.544 607.488 
L 289.44 607.488 
L 289.44 575.04 
L 176.544 575.04 
z
" clip-path="url(#p64a9a70894)" style="fill: #2196f3; stroke: #1976d2; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_8">
    <path d="M 204.768 619.656 
C 208.510546 619.656 212.100311 618.801269 214.746691 617.28005 
C 217.393071 615.758832 218.88 613.695328 218.88 611.544 
C 218.88 609.392672 217.393071 607.329168 214.746691 605.80795 
C 212.100311 604.286731 208.510546 603.432 204.768 603.432 
C 201.025454 603.432 197.435689 604.286731 194.789309 605.80795 
C 192.142929 607.329168 190.656 609.392672 190.656 611.544 
C 190.656 613.695328 192.142929 615.758832 194.789309 617.28005 
C 197.435689 618.801269 201.025454 619.656 204.768 619.656 
z
" clip-path="url(#p64a9a70894)" style="fill: #1976d2; stroke: #1976d2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_9">
    <path d="M 261.216 619.656 
C 264.958546 619.656 268.548311 618.801269 271.194691 617.28005 
C 273.841071 615.758832 275.328 613.695328 275.328 611.544 
C 275.328 609.392672 273.841071 607.329168 271.194691 605.80795 
C 268.548311 604.286731 264.958546 603.432 261.216 603.432 
C 257.473454 603.432 253.883689 604.286731 251.237309 605.80795 
C 248.590929 607.329168 247.104 609.392672 247.104 611.544 
C 247.104 613.695328 248.590929 615.758832 251.237309 617.28005 
C 253.883689 618.801269 257.473454 619.656 261.216 619.656 
z
" clip-path="url(#p64a9a70894)" style="fill: #1976d2; stroke: #1976d2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_10">
    <path d="M 1347.84 607.488 
L 1432.512 607.488 
L 1432.512 566.928 
L 1347.84 566.928 
z
" clip-path="url(#p64a9a70894)" style="fill: #f44336; stroke: #d32f2f; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_11">
    <path d="M 176.544 561.2496 
L 289.44 561.2496 
Q 293.6736 561.2496 293.6736 558.816 
L 293.6736 526.368 
Q 293.6736 523.9344 289.44 523.9344 
L 176.544 523.9344 
Q 172.3104 523.9344 172.3104 526.368 
L 172.3104 558.816 
Q 172.3104 561.2496 176.544 561.2496 
z
" clip-path="url(#p64a9a70894)" style="fill: #fff59d; stroke: #fbc02d; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_12">
    <path d="M 289.44 834.624 
L 501.12 834.624 
L 501.12 802.176 
L 289.44 802.176 
z
" clip-path="url(#p64a9a70894)" style="fill: #ff9800; opacity: 0.3; stroke: #ff9800; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="patch_13">
    <path d="M 501.12 834.624 
L 924.48 834.624 
L 924.48 802.176 
L 501.12 802.176 
z
" clip-path="url(#p64a9a70894)" style="fill: #f44336; opacity: 0.3; stroke: #f44336; stroke-width: 2; stroke-linejoin: miter"/>
   </g>
   <g id="line2d_1">
    <path d="M 148.32 615.6 
L 1559.52 615.6 
" clip-path="url(#p64a9a70894)" style="fill: none; stroke: #000000; stroke-width: 5; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 289.44 639.936 
L 1347.84 639.936 
" clip-path="url(#p64a9a70894)" style="fill: none; stroke-dasharray: 14.8,6.4; stroke-dashoffset: 0; stroke: #4caf50; stroke-width: 4"/>
   </g>
   <g id="line2d_3">
    <path d="M 148.32 737.28 
L 1559.52 737.28 
" clip-path="url(#p64a9a70894)" style="fill: none; stroke: #666666; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_4">
    <path d="M 289.44 745.392 
L 289.44 729.168 
" clip-path="url(#p64a9a70894)" style="fill: none; stroke: #000000; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_5">
    <path d="M 501.12 745.392 
L 501.12 729.168 
" clip-path="url(#p64a9a70894)" style="fill: none; stroke: #000000; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_6">
    <path d="M 924.48 745.392 
L 924.48 729.168 
" clip-path="url(#p64a9a70894)" style="fill: none; stroke: #000000; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="text_1">
    <!-- AEB制动距离计算示意图 -->
    <g transform="translate(685.92 58.6975) scale(0.32 -0.32)">
     <defs>
      <path id="SimHei-41" d="M 3075 125 
L 2475 125 
L 2125 1450 
L 1025 1450 
L 675 125 
L 75 125 
L 1325 4450 
L 1825 4450 
L 3075 125 
z
M 2000 1925 
L 1600 3450 
L 1550 3450 
L 1150 1925 
L 2000 1925 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-45" d="M 2900 125 
L 350 125 
L 350 4400 
L 2775 4400 
L 2775 3925 
L 925 3925 
L 925 2600 
L 2625 2600 
L 2625 2125 
L 925 2125 
L 925 600 
L 2900 600 
L 2900 125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-42" d="M 2950 1400 
Q 2950 675 2550 400 
Q 2150 125 1525 125 
L 275 125 
L 275 4400 
L 1575 4400 
Q 2200 4400 2512 4075 
Q 2825 3750 2825 3300 
Q 2825 2850 2612 2637 
Q 2400 2425 2175 2350 
Q 2475 2275 2712 2037 
Q 2950 1800 2950 1400 
z
M 2250 3300 
Q 2250 3625 2062 3775 
Q 1875 3925 1600 3925 
L 850 3925 
L 850 2600 
L 1575 2600 
Q 1850 2600 2050 2762 
Q 2250 2925 2250 3300 
z
M 2350 1400 
Q 2350 1800 2087 1962 
Q 1825 2125 1500 2125 
L 850 2125 
L 850 600 
L 1425 600 
Q 1875 600 2112 775 
Q 2350 950 2350 1400 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5236" d="M 1325 4625 
Q 1175 4375 1075 4150 
L 1800 4150 
Q 1800 4775 1775 5125 
L 2350 5125 
Q 2325 4800 2325 4150 
L 2700 4150 
Q 3200 4150 3600 4175 
L 3600 3725 
Q 3250 3750 2725 3750 
L 2325 3750 
L 2325 3025 
L 2950 3025 
Q 3325 3025 3750 3050 
L 3750 2625 
L 2325 2625 
L 2325 1950 
L 3675 1950 
Q 3650 1650 3637 1125 
Q 3625 600 3612 412 
Q 3600 225 3400 137 
Q 3200 50 2825 -25 
Q 2775 250 2650 550 
Q 2975 550 3050 587 
Q 3125 625 3125 975 
L 3125 1550 
L 2325 1550 
L 2325 175 
Q 2325 -250 2350 -550 
L 1775 -550 
Q 1800 -75 1800 175 
L 1800 1550 
L 1025 1550 
L 1025 -25 
L 500 -25 
Q 525 300 525 650 
L 525 1200 
Q 525 1675 500 1950 
L 1800 1950 
L 1800 2625 
L 1000 2625 
Q 550 2625 225 2600 
L 225 3050 
Q 550 3025 1000 3025 
L 1800 3025 
L 1800 3750 
L 925 3750 
Q 800 3450 675 3250 
Q 450 3400 225 3500 
Q 400 3750 525 4075 
Q 650 4400 750 4800 
Q 1000 4700 1325 4625 
z
M 5875 5075 
Q 5850 4825 5850 4350 
L 5850 25 
Q 5850 -325 5587 -412 
Q 5325 -500 4825 -575 
Q 4750 -250 4625 0 
Q 5050 0 5200 25 
Q 5350 50 5350 350 
L 5350 4350 
Q 5350 4775 5325 5075 
L 5875 5075 
z
M 4675 4325 
Q 4650 3975 4650 3700 
L 4650 1625 
Q 4650 1275 4675 775 
L 4125 775 
Q 4175 1200 4175 1625 
L 4175 3700 
Q 4175 3950 4125 4325 
L 4675 4325 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-52a8" d="M 4125 5150 
Q 4425 5075 4700 5075 
Q 4650 4825 4637 4512 
Q 4625 4200 4625 3750 
L 6025 3750 
Q 6000 3425 5975 3025 
L 5850 175 
Q 5775 -325 5437 -437 
Q 5100 -550 4675 -600 
Q 4600 -300 4425 -50 
Q 4975 -75 5175 12 
Q 5375 100 5375 350 
L 5525 3325 
L 4625 3325 
Q 4575 2225 4462 1650 
Q 4350 1075 4112 587 
Q 3875 100 3150 -675 
Q 2900 -425 2700 -350 
Q 3175 25 3475 437 
Q 3775 850 3900 1287 
Q 4025 1725 4075 2200 
Q 4125 2675 4150 3325 
L 3725 3325 
Q 3450 3325 3075 3300 
L 3075 3775 
Q 3450 3750 3775 3750 
L 4150 3750 
L 4150 4450 
Q 4150 4800 4125 5150 
z
M 275 3025 
Q 650 3000 1175 3000 
L 2125 3000 
Q 2575 3000 3150 3025 
L 3150 2575 
Q 2575 2600 2150 2600 
L 1875 2600 
Q 1725 2200 1537 1750 
Q 1350 1300 1100 850 
Q 2075 950 2575 1050 
Q 2450 1350 2175 1800 
Q 2450 1950 2575 2050 
Q 2825 1600 3000 1287 
Q 3175 975 3375 525 
Q 3175 400 2925 275 
Q 2825 500 2750 650 
Q 1950 525 1462 450 
Q 975 375 625 250 
Q 525 500 375 725 
Q 700 925 962 1525 
Q 1225 2125 1350 2600 
L 1175 2600 
Q 650 2600 275 2575 
L 275 3025 
z
M 2000 4425 
Q 2425 4425 2950 4450 
L 2950 3975 
Q 2425 4000 2000 4000 
L 1400 4000 
Q 1000 4000 625 3975 
L 625 4450 
Q 975 4425 1375 4425 
L 2000 4425 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8ddd" d="M 3550 1275 
L 3550 25 
L 5250 25 
Q 5750 25 6125 50 
L 6125 -425 
Q 5750 -400 5275 -400 
L 3075 -400 
Q 3100 -75 3100 575 
L 3100 4075 
Q 3100 4575 3075 4950 
L 5200 4950 
Q 5550 4950 6000 4975 
L 6000 4525 
Q 5550 4550 5225 4550 
L 3550 4550 
L 3550 3325 
L 5650 3325 
Q 5625 3050 5625 2300 
Q 5625 1575 5650 1275 
L 3550 1275 
z
M 2650 4950 
Q 2625 4400 2625 3900 
Q 2625 3400 2650 3050 
L 2000 3050 
L 2000 2175 
Q 2375 2175 2875 2200 
L 2875 1725 
Q 2375 1750 2000 1750 
L 2000 525 
Q 2625 625 2875 700 
L 2875 250 
Q 1375 -50 1062 -150 
Q 750 -250 425 -375 
Q 350 -75 250 150 
L 625 225 
L 625 1775 
Q 625 2050 600 2325 
L 1100 2325 
Q 1075 2050 1075 1775 
L 1075 300 
L 1575 425 
L 1575 3050 
L 700 3050 
Q 725 3425 725 3900 
Q 725 4400 700 4950 
L 2650 4950 
z
M 5150 1700 
L 5150 2925 
L 3550 2925 
L 3550 1700 
L 5150 1700 
z
M 2200 3425 
L 2200 4575 
L 1150 4575 
L 1150 3425 
L 2200 3425 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-79bb" d="M 3000 2275 
Q 3150 2175 3425 2050 
Q 3275 1900 3150 1725 
L 5825 1725 
L 5825 0 
Q 5825 -475 5612 -537 
Q 5400 -600 4850 -650 
Q 4775 -350 4650 -100 
Q 5225 -125 5300 -87 
Q 5375 -50 5375 175 
L 5375 1350 
L 2950 1350 
Q 2825 1150 2675 962 
Q 2525 775 2350 525 
Q 2650 525 4075 675 
Q 3975 800 3850 950 
Q 3975 1075 4200 1225 
Q 4350 1075 4537 825 
Q 4725 575 4900 300 
Q 4650 200 4475 50 
Q 4350 200 4275 350 
Q 3175 225 2700 150 
Q 2225 75 1850 -25 
Q 1800 275 1675 600 
Q 1875 700 2075 900 
Q 2275 1100 2450 1350 
L 1125 1350 
L 1125 -625 
L 650 -625 
Q 675 -225 675 225 
L 675 1000 
Q 675 1450 650 1725 
L 2625 1725 
Q 2800 2000 2925 2275 
L 1025 2275 
Q 1050 2525 1050 2725 
L 1050 3325 
Q 1050 3550 1025 3800 
L 1525 3800 
Q 1500 3525 1500 3275 
L 1500 2650 
L 5050 2650 
L 5050 3325 
Q 5050 3575 5025 3775 
L 5525 3775 
Q 5500 3575 5500 3325 
L 5500 2750 
Q 5500 2550 5525 2275 
L 3000 2275 
z
M 3300 5275 
Q 3400 5000 3525 4650 
L 4950 4650 
Q 5375 4650 5950 4675 
L 5950 4275 
Q 5375 4300 4775 4300 
L 1525 4300 
Q 1050 4300 475 4275 
L 475 4675 
Q 1025 4650 1050 4650 
L 2975 4650 
Q 2900 4900 2750 5150 
Q 3025 5200 3300 5275 
z
M 2175 4125 
Q 2450 4050 2750 3937 
Q 3050 3825 3350 3725 
Q 3850 4025 4125 4250 
Q 4375 4100 4650 3925 
Q 4200 3800 3825 3550 
Q 4250 3350 4800 3100 
Q 4625 2950 4375 2775 
Q 4025 3000 3787 3112 
Q 3550 3225 3325 3300 
Q 3125 3175 2825 3050 
Q 2525 2925 2150 2775 
Q 2000 2975 1800 3125 
Q 2175 3200 2412 3287 
Q 2650 3375 2950 3475 
Q 2625 3575 2425 3637 
Q 2225 3700 1850 3775 
Q 2050 3950 2175 4125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8ba1" d="M 3925 3100 
L 3925 4350 
Q 3925 4850 3900 5125 
L 4425 5125 
Q 4400 4800 4400 4550 
L 4400 3100 
L 5425 3100 
Q 5725 3100 6075 3125 
L 6075 2650 
Q 5700 2675 5450 2675 
L 4400 2675 
L 4400 175 
Q 4400 -125 4425 -550 
L 3900 -550 
Q 3925 -125 3925 150 
L 3925 2675 
L 2950 2675 
Q 2575 2675 2200 2650 
L 2200 3125 
Q 2575 3100 2900 3100 
L 3925 3100 
z
M 1775 3150 
Q 1750 2675 1750 2150 
L 1750 625 
Q 2175 1050 2425 1400 
Q 2575 1200 2775 1000 
L 2075 300 
Q 1750 -25 1450 -350 
Q 1275 -125 1125 50 
Q 1300 250 1275 650 
L 1275 2725 
Q 600 2725 275 2700 
L 275 3175 
Q 675 3150 825 3150 
L 1775 3150 
z
M 1150 5050 
Q 1475 4725 2175 4000 
Q 1950 3800 1750 3625 
Q 1425 4100 775 4750 
Q 950 4875 1150 5050 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7b97" d="M 5175 3650 
Q 5150 3200 5150 2500 
Q 5150 1800 5175 1325 
L 4575 1325 
L 4575 875 
L 5250 875 
Q 5650 875 6150 900 
L 6150 475 
Q 5650 500 5250 500 
L 4575 500 
Q 4575 -75 4600 -625 
L 4050 -625 
Q 4075 -75 4075 500 
L 2650 500 
Q 2475 100 2125 -175 
Q 1775 -450 1075 -675 
Q 925 -425 700 -250 
Q 1250 -150 1600 37 
Q 1950 225 2125 500 
L 1225 500 
Q 800 500 300 475 
L 300 900 
Q 825 875 1225 875 
L 2200 875 
Q 2250 1075 2250 1325 
L 1250 1325 
Q 1275 1775 1275 2475 
Q 1275 3200 1250 3650 
L 5175 3650 
z
M 1825 5100 
Q 1625 4900 1475 4725 
L 2425 4725 
Q 2750 4725 3175 4750 
L 3175 4325 
Q 2750 4350 2200 4350 
Q 2250 4175 2350 3875 
Q 2075 3850 1825 3750 
Q 1750 4075 1675 4350 
L 1200 4350 
Q 1075 4125 775 3775 
Q 525 3925 250 4000 
Q 500 4225 762 4537 
Q 1025 4850 1275 5300 
Q 1500 5175 1825 5100 
z
M 4525 5100 
Q 4325 4950 4175 4725 
L 5225 4725 
Q 5500 4725 5950 4750 
L 5950 4325 
Q 5550 4350 4975 4350 
L 5175 3950 
Q 4925 3925 4650 3825 
Q 4575 4100 4500 4350 
L 3900 4350 
Q 3700 4025 3525 3775 
Q 3250 3950 3025 4000 
Q 3325 4300 3550 4612 
Q 3775 4925 3900 5275 
Q 4175 5175 4525 5100 
z
M 4650 1675 
L 4650 2000 
L 1775 2000 
L 1775 1675 
L 4650 1675 
z
M 4650 2350 
L 4650 2650 
L 1775 2650 
L 1775 2350 
L 4650 2350 
z
M 4650 3000 
L 4650 3300 
L 1775 3300 
L 1775 3000 
L 4650 3000 
z
M 4075 875 
L 4075 1325 
L 2725 1325 
Q 2725 1075 2700 875 
L 4075 875 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-793a" d="M 5175 3050 
Q 5500 3050 5975 3075 
L 5975 2575 
Q 5500 2625 5150 2625 
L 3500 2625 
L 3500 -75 
Q 3500 -350 3212 -487 
Q 2925 -625 2425 -675 
Q 2400 -425 2175 -100 
Q 2625 -125 2825 -87 
Q 3025 -50 3025 175 
L 3025 2625 
L 1400 2625 
Q 900 2625 500 2600 
L 500 3075 
Q 925 3050 1400 3050 
L 5175 3050 
z
M 4425 4775 
Q 4775 4775 5225 4800 
L 5225 4325 
Q 4775 4350 4425 4350 
L 2125 4350 
Q 1600 4350 1200 4325 
L 1200 4800 
Q 1600 4775 2125 4775 
L 4425 4775 
z
M 2225 1825 
Q 1950 1400 1650 900 
Q 1350 400 975 -125 
Q 775 75 500 150 
Q 900 600 1200 1087 
Q 1500 1575 1725 2075 
Q 1975 1925 2225 1825 
z
M 4500 2025 
Q 5450 925 5925 275 
Q 5650 125 5475 -25 
Q 5100 550 4125 1750 
Q 4325 1850 4500 2025 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-610f" d="M 5175 2900 
Q 5150 2425 5150 1975 
Q 5150 1550 5175 1050 
L 1200 1050 
Q 1225 1550 1225 2000 
Q 1225 2450 1200 2900 
L 5175 2900 
z
M 1575 4250 
Q 1125 4250 625 4225 
L 625 4650 
Q 1150 4625 1575 4625 
L 3075 4625 
Q 3050 4825 2900 5150 
Q 3175 5175 3475 5250 
Q 3575 4800 3625 4625 
L 4775 4625 
Q 5275 4625 5650 4650 
L 5650 4225 
Q 5225 4250 4775 4250 
L 2275 4250 
Q 2450 4000 2575 3775 
L 2225 3625 
L 3800 3625 
Q 3900 3750 4025 4200 
Q 4325 4125 4575 4050 
Q 4400 3825 4300 3625 
L 5050 3625 
Q 5600 3625 6075 3650 
L 6075 3225 
Q 5600 3250 5050 3250 
L 1300 3250 
Q 775 3250 325 3225 
L 325 3650 
Q 800 3625 1300 3625 
L 2125 3625 
Q 2050 3825 1875 4050 
Q 2025 4125 2225 4250 
L 1575 4250 
z
M 2325 725 
Q 2300 450 2300 275 
Q 2300 125 2312 0 
Q 2325 -125 2550 -125 
L 4025 -125 
Q 4175 -75 4275 450 
Q 4475 275 4825 175 
Q 4650 -300 4525 -400 
Q 4400 -500 4100 -500 
L 2275 -500 
Q 1850 -475 1825 -287 
Q 1800 -100 1800 150 
Q 1800 400 1750 725 
L 2325 725 
z
M 4650 2150 
L 4650 2550 
L 1725 2550 
L 1725 2150 
L 4650 2150 
z
M 4650 1425 
L 4650 1775 
L 1725 1775 
L 1725 1425 
L 4650 1425 
z
M 5400 850 
Q 5875 225 6125 -75 
Q 5900 -250 5725 -375 
Q 5550 -100 5000 575 
Q 5150 700 5400 850 
z
M 1275 575 
Q 1050 100 800 -550 
Q 500 -425 300 -375 
Q 600 25 800 725 
Q 1025 625 1275 575 
z
M 3325 975 
Q 3600 600 3775 350 
Q 3550 200 3325 75 
Q 3250 300 2900 700 
Q 3050 825 3325 975 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-56fe" d="M 5800 4975 
Q 5775 4350 5775 3600 
L 5775 925 
Q 5775 150 5800 -600 
L 5300 -600 
L 5300 -275 
L 1125 -275 
L 1125 -650 
L 625 -650 
Q 650 125 650 950 
L 650 3600 
Q 650 4325 625 4975 
L 5800 4975 
z
M 5300 150 
L 5300 4575 
L 1125 4575 
L 1125 150 
L 5300 150 
z
M 3225 4250 
Q 3075 4100 2950 3925 
L 4625 3925 
Q 4525 3700 4300 3375 
Q 4075 3050 3600 2600 
Q 4350 2225 5075 2150 
Q 4900 1925 4775 1675 
Q 3850 1950 3225 2350 
Q 2500 1875 1675 1650 
Q 1600 1875 1400 2075 
Q 2200 2200 2875 2600 
Q 2525 2925 2375 3150 
Q 2150 2900 1900 2650 
Q 1725 2850 1525 2950 
Q 1950 3300 2250 3737 
Q 2550 4175 2675 4475 
Q 2925 4350 3225 4250 
z
M 2450 1275 
Q 3550 950 4100 725 
Q 3975 525 3850 300 
Q 2825 725 2275 850 
Q 2375 1050 2450 1275 
z
M 2925 2050 
Q 3775 1700 4075 1600 
Q 3950 1400 3850 1150 
Q 3000 1550 2700 1650 
Q 2825 1825 2925 2050 
z
M 2625 3450 
Q 3000 3025 3275 2850 
Q 3600 3100 3875 3525 
L 2675 3525 
L 2625 3450 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-41"/>
     <use xlink:href="#SimHei-45" transform="translate(50 0)"/>
     <use xlink:href="#SimHei-42" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-5236" transform="translate(150 0)"/>
     <use xlink:href="#SimHei-52a8" transform="translate(250 0)"/>
     <use xlink:href="#SimHei-8ddd" transform="translate(350 0)"/>
     <use xlink:href="#SimHei-79bb" transform="translate(450 0)"/>
     <use xlink:href="#SimHei-8ba1" transform="translate(550 0)"/>
     <use xlink:href="#SimHei-7b97" transform="translate(650 0)"/>
     <use xlink:href="#SimHei-793a" transform="translate(750 0)"/>
     <use xlink:href="#SimHei-610f" transform="translate(850 0)"/>
     <use xlink:href="#SimHei-56fe" transform="translate(950 0)"/>
    </g>
   </g>
   <g id="text_2">
    <!-- 已知参数 -->
    <g transform="translate(245.44 120.768) scale(0.22 -0.22)">
     <defs>
      <path id="SimHei-5df2" d="M 5275 4725 
Q 5250 4275 5250 3850 
L 5250 2925 
Q 5250 2400 5275 1900 
L 4725 1900 
L 4725 2275 
L 1425 2275 
L 1425 600 
Q 1375 0 2050 25 
L 4525 25 
Q 4950 0 5112 150 
Q 5275 300 5350 1000 
Q 5625 825 5975 725 
Q 5825 50 5637 -187 
Q 5450 -425 5100 -450 
L 1750 -450 
Q 850 -475 900 325 
L 900 2475 
Q 900 2725 875 3400 
L 1475 3400 
Q 1450 3050 1450 2700 
L 4725 2700 
L 4725 4275 
L 1950 4275 
Q 1375 4275 650 4250 
L 650 4750 
Q 1375 4725 1925 4725 
L 5275 4725 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-77e5" d="M 5925 4350 
Q 5900 3850 5900 3450 
L 5900 850 
Q 5900 375 5925 -125 
L 5400 -125 
L 5400 550 
L 4075 550 
L 4075 -225 
L 3550 -225 
Q 3575 250 3575 775 
L 3575 3450 
Q 3575 3850 3550 4350 
L 5925 4350 
z
M 500 -275 
Q 825 0 1087 387 
Q 1350 775 1525 1187 
Q 1700 1600 1750 2000 
Q 825 2000 425 1975 
L 425 2450 
Q 800 2425 1825 2425 
Q 1900 2950 1900 3700 
L 1325 3700 
Q 1200 3350 950 2875 
Q 675 2950 450 3000 
Q 625 3250 800 3662 
Q 975 4075 1087 4462 
Q 1200 4850 1250 5150 
Q 1500 5075 1825 5025 
Q 1700 4850 1625 4625 
Q 1550 4400 1450 4125 
Q 2875 4125 3225 4150 
L 3225 3700 
L 2375 3700 
Q 2375 3150 2325 2425 
Q 2925 2425 3375 2450 
L 3375 1975 
Q 2925 2000 2250 2000 
Q 2225 1725 2150 1475 
Q 2375 1300 2650 1087 
Q 2925 875 3350 375 
Q 3175 300 2950 25 
Q 2725 400 2487 637 
Q 2250 875 2000 1075 
Q 1850 675 1537 225 
Q 1225 -225 900 -575 
Q 775 -425 500 -275 
z
M 5400 950 
L 5400 3950 
L 4075 3950 
L 4075 950 
L 5400 950 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-53c2" d="M 3325 4850 
Q 3100 4725 2850 4500 
Q 2600 4275 2175 3925 
Q 2850 3950 3275 3950 
Q 3700 3950 4200 3975 
Q 3950 4200 3600 4450 
Q 3800 4625 3925 4775 
Q 4900 4100 5325 3675 
Q 5100 3475 4925 3300 
Q 4750 3475 4550 3625 
Q 3625 3600 3200 3575 
Q 3025 3250 2925 3125 
L 5075 3125 
Q 5575 3125 5950 3150 
L 5950 2700 
Q 5600 2725 5075 2725 
L 4325 2725 
Q 4675 2350 5187 2075 
Q 5700 1800 6150 1700 
Q 5850 1550 5675 1225 
Q 5100 1525 4612 1900 
Q 4125 2275 3800 2725 
L 2725 2725 
Q 2325 2300 1812 1962 
Q 1300 1625 650 1325 
Q 525 1525 250 1700 
Q 725 1825 1237 2100 
Q 1750 2375 2100 2725 
L 1475 2725 
Q 950 2725 375 2700 
L 375 3150 
Q 875 3125 1475 3125 
L 2400 3125 
L 2550 3350 
L 2650 3550 
Q 1925 3500 1775 3475 
Q 1625 3450 1500 3425 
Q 1375 3725 1300 3875 
Q 1600 3950 2050 4337 
Q 2500 4725 2825 5225 
Q 3025 5050 3325 4850 
z
M 5350 800 
Q 5025 650 4612 437 
Q 4200 225 3850 75 
Q 3500 -75 3062 -212 
Q 2625 -350 2150 -450 
Q 1675 -550 1225 -600 
Q 1150 -350 900 -150 
Q 1675 -125 2362 25 
Q 3050 175 3512 387 
Q 3975 600 4312 800 
Q 4650 1000 4900 1250 
Q 5100 1025 5350 800 
z
M 4550 1450 
Q 4350 1375 4037 1200 
Q 3725 1025 3350 862 
Q 2975 700 2512 550 
Q 2050 400 1575 325 
Q 1450 575 1250 775 
Q 1775 800 2175 900 
Q 2575 1000 3075 1212 
Q 3575 1425 4125 1850 
Q 4300 1675 4550 1450 
z
M 3900 2075 
Q 3575 1950 3350 1825 
Q 3125 1700 2687 1525 
Q 2250 1350 1750 1200 
Q 1625 1425 1450 1600 
Q 2025 1700 2587 1937 
Q 3150 2175 3500 2500 
Q 3700 2275 3900 2075 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6570" d="M 4525 4925 
Q 4475 4800 4400 4587 
Q 4325 4375 4175 3825 
L 5325 3825 
Q 5575 3825 5925 3850 
L 5925 3400 
Q 5650 3425 5500 3425 
Q 5500 3025 5362 2162 
Q 5225 1300 4875 675 
Q 5125 375 5437 137 
Q 5750 -100 6025 -200 
Q 5725 -425 5625 -625 
Q 5325 -450 5075 -225 
Q 4825 0 4575 325 
Q 4300 25 3987 -187 
Q 3675 -400 3200 -650 
Q 3075 -425 2850 -300 
Q 3225 -150 3650 125 
Q 4075 400 4300 675 
Q 4125 1025 3962 1425 
Q 3800 1825 3675 2475 
Q 3600 2275 3425 1950 
Q 3250 2050 3000 2150 
Q 3400 2850 3650 3675 
Q 3900 4500 3975 5100 
Q 4275 4975 4525 4925 
z
M 2100 2250 
Q 2000 2150 1875 1925 
L 3250 1925 
Q 3100 1300 2725 675 
Q 3125 525 3350 400 
Q 3225 225 3125 25 
Q 2900 175 2450 325 
Q 1950 -225 725 -650 
Q 600 -375 400 -275 
Q 1575 0 2000 475 
Q 1300 650 850 775 
Q 1000 1000 1250 1525 
Q 1000 1525 425 1500 
L 425 1950 
Q 900 1925 1400 1925 
Q 1500 2150 1550 2425 
Q 1825 2325 2100 2250 
z
M 1775 3950 
Q 1775 4550 1750 5100 
L 2250 5100 
Q 2225 4575 2225 3950 
Q 3075 3950 3425 3975 
L 3425 3525 
Q 3075 3550 2225 3550 
Q 2225 2825 2250 2425 
L 1750 2425 
Q 1775 2775 1775 3300 
Q 1650 3100 1300 2787 
Q 950 2475 650 2300 
Q 525 2525 275 2625 
Q 500 2700 875 2975 
Q 1250 3250 1450 3550 
Q 950 3550 500 3525 
L 500 3975 
Q 925 3950 1775 3950 
z
M 3975 3175 
Q 4200 1700 4600 1125 
Q 4850 1700 4937 2287 
Q 5025 2875 5050 3425 
L 4075 3425 
L 3975 3175 
z
M 1450 1000 
Q 1750 925 2275 800 
Q 2475 1050 2650 1525 
L 1725 1525 
Q 1600 1275 1450 1000 
z
M 3325 4825 
Q 3200 4625 3125 4450 
Q 3050 4275 2925 4025 
Q 2725 4125 2525 4175 
Q 2725 4500 2900 5000 
Q 3150 4875 3325 4825 
z
M 2575 3375 
Q 2900 3050 3200 2725 
Q 3050 2600 2875 2425 
Q 2525 2850 2300 3100 
Q 2450 3225 2575 3375 
z
M 925 5000 
Q 1275 4650 1500 4300 
L 1125 4075 
Q 950 4425 600 4725 
Q 825 4875 925 5000 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-5df2"/>
     <use xlink:href="#SimHei-77e5" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-53c2" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-6570" transform="translate(300 0)"/>
    </g>
   </g>
   <g id="text_3">
    <!-- 测试车速： -->
    <g transform="translate(120.096 153.216) scale(0.18 -0.18)">
     <defs>
      <path id="SimHei-6d4b" d="M 4025 4725 
Q 4000 4475 4000 4000 
L 4000 2225 
Q 4000 1775 4025 1350 
L 3550 1350 
L 3550 4325 
L 2350 4325 
L 2350 1325 
L 1900 1325 
Q 1925 1775 1925 2225 
L 1925 4000 
Q 1925 4200 1900 4725 
L 4025 4725 
z
M 5900 5075 
Q 5875 4800 5875 4375 
L 5875 0 
Q 5875 -325 5650 -425 
Q 5425 -525 4975 -600 
Q 4925 -325 4775 -50 
Q 5125 -50 5262 -25 
Q 5400 0 5400 250 
L 5400 4375 
Q 5400 4775 5375 5075 
L 5900 5075 
z
M 3275 3725 
Q 3225 3400 3200 2325 
Q 3175 1250 2837 550 
Q 2500 -150 2075 -600 
Q 1850 -400 1600 -325 
Q 2200 250 2475 962 
Q 2750 1675 2762 2475 
Q 2775 3275 2725 3800 
Q 3000 3750 3275 3725 
z
M 4950 4275 
Q 4925 4000 4925 3575 
L 4925 1825 
Q 4925 1300 4950 850 
L 4450 850 
Q 4475 1150 4475 1825 
L 4475 3575 
Q 4475 4000 4450 4275 
L 4950 4275 
z
M 1700 1675 
Q 1375 1075 1112 487 
Q 850 -100 725 -400 
Q 500 -200 200 -25 
Q 475 300 762 850 
Q 1050 1400 1250 2000 
Q 1475 1825 1700 1675 
z
M 3400 1050 
Q 3800 650 4300 -125 
Q 4100 -225 3875 -450 
Q 3475 275 3025 750 
Q 3225 875 3400 1050 
z
M 600 3525 
Q 1125 3125 1500 2775 
Q 1300 2575 1150 2375 
Q 825 2775 300 3150 
Q 450 3300 600 3525 
z
M 1350 4000 
Q 950 4375 475 4675 
Q 625 4850 775 5050 
Q 1300 4700 1675 4375 
Q 1500 4250 1350 4000 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8bd5" d="M 3950 3825 
Q 3925 4325 3925 4550 
Q 3925 4800 3900 5225 
Q 4200 5150 4450 5125 
Q 4425 4825 4425 4600 
L 4425 3825 
L 5150 3825 
Q 5500 3825 5875 3850 
L 5875 3400 
Q 5525 3425 5125 3425 
L 4450 3425 
Q 4500 2750 4587 2212 
Q 4675 1675 4837 1112 
Q 5000 550 5212 225 
Q 5425 -100 5500 200 
Q 5575 500 5600 900 
Q 5800 625 6100 525 
Q 5975 -275 5750 -487 
Q 5525 -700 5250 -537 
Q 4975 -375 4737 62 
Q 4500 500 4337 1075 
Q 4175 1650 4100 2262 
Q 4025 2875 3975 3425 
L 2800 3425 
Q 2300 3425 1875 3400 
L 1875 3850 
Q 2275 3825 2775 3825 
L 3950 3825 
z
M 1500 3175 
Q 1475 2675 1475 2175 
L 1475 625 
Q 1825 950 2125 1225 
Q 2225 1000 2375 800 
Q 1900 425 1125 -350 
Q 1000 -125 825 50 
Q 1025 175 1025 525 
L 1025 2725 
Q 625 2725 300 2700 
L 300 3200 
Q 625 3175 825 3175 
L 1500 3175 
z
M 3775 2175 
Q 3500 2200 3250 2200 
L 3250 575 
Q 3575 675 3900 800 
Q 3900 525 3950 325 
Q 3575 250 3150 125 
Q 2725 0 2275 -175 
Q 2200 100 2025 300 
Q 2425 350 2800 475 
L 2800 2200 
Q 2525 2200 2225 2175 
L 2225 2650 
Q 2600 2625 3000 2625 
Q 3425 2625 3775 2650 
L 3775 2175 
z
M 925 4975 
Q 1275 4650 1700 4100 
Q 1525 3925 1325 3775 
Q 1175 4050 575 4650 
Q 775 4825 925 4975 
z
M 5150 5150 
Q 5325 5000 5850 4375 
Q 5575 4200 5425 4050 
Q 5125 4525 4775 4875 
Q 4925 4975 5150 5150 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8f66" d="M 1125 1950 
Q 1000 2300 900 2525 
Q 1225 2650 1562 3062 
Q 1900 3475 2125 3850 
Q 1200 3850 625 3825 
L 625 4325 
Q 1150 4300 2375 4300 
Q 2675 4900 2775 5275 
Q 3075 5125 3350 5025 
Q 3100 4675 2900 4300 
L 4600 4300 
Q 4900 4300 5525 4325 
L 5525 3825 
Q 5025 3850 4600 3850 
L 2675 3850 
Q 2400 3425 1725 2550 
L 3125 2550 
Q 3125 3025 3100 3525 
L 3650 3525 
Q 3625 3100 3625 2550 
L 4475 2550 
Q 4975 2550 5450 2575 
L 5450 2075 
Q 4975 2100 4475 2100 
L 3625 2100 
L 3625 1150 
L 5025 1150 
Q 5500 1150 6075 1175 
L 6075 650 
Q 5500 675 5025 675 
L 3625 675 
Q 3625 -125 3650 -600 
L 3100 -600 
Q 3125 -100 3125 675 
L 1400 675 
Q 850 675 350 650 
L 350 1175 
Q 850 1150 1400 1150 
L 3125 1150 
L 3125 2100 
Q 2225 2100 1875 2075 
Q 1525 2050 1125 1950 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-901f" d="M 3725 4600 
Q 3725 4975 3700 5250 
L 4175 5250 
Q 4150 5000 4150 4600 
L 5175 4600 
Q 5550 4600 5925 4625 
L 5925 4175 
Q 5550 4200 5175 4200 
L 4150 4200 
L 4150 3725 
L 5700 3725 
Q 5675 3325 5675 2950 
Q 5675 2575 5700 2200 
L 4150 2200 
Q 4150 725 4175 225 
L 3700 225 
Q 3725 725 3725 1925 
Q 3425 1475 3100 1125 
Q 2775 775 2300 425 
Q 2125 625 1825 775 
Q 2275 950 2712 1387 
Q 3150 1825 3325 2200 
L 2200 2200 
Q 2225 2625 2225 2950 
Q 2225 3300 2200 3725 
L 3725 3725 
L 3725 4200 
L 2675 4200 
Q 2325 4200 1925 4175 
L 1925 4625 
Q 2325 4600 2675 4600 
L 3725 4600 
z
M 1500 2875 
Q 1475 2400 1475 1925 
L 1475 550 
Q 2150 -25 3725 -37 
Q 5300 -50 6200 100 
Q 6025 -175 6000 -525 
Q 3925 -550 3087 -475 
Q 2250 -400 1800 -137 
Q 1350 125 1112 -62 
Q 875 -250 600 -600 
Q 475 -375 200 -200 
Q 475 25 1000 400 
L 1000 2450 
Q 675 2450 275 2425 
L 275 2900 
Q 700 2875 1000 2875 
L 1500 2875 
z
M 4550 2050 
Q 4950 1700 5925 925 
Q 5725 775 5550 550 
Q 5075 1000 4250 1675 
Q 4400 1825 4550 2050 
z
M 3725 2575 
L 3725 3325 
L 2675 3325 
L 2675 2575 
L 3725 2575 
z
M 5200 2575 
L 5200 3325 
L 4150 3325 
L 4150 2575 
L 5200 2575 
z
M 1100 4950 
Q 1425 4350 1600 3850 
Q 1350 3750 1050 3625 
Q 975 4050 650 4800 
Q 900 4850 1100 4950 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-ff1a" d="M 1200 2425 
L 2025 2425 
L 2025 1600 
L 1200 1600 
L 1200 2425 
z
M 1200 825 
L 2025 825 
L 2025 0 
L 1200 0 
L 1200 825 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-6d4b"/>
     <use xlink:href="#SimHei-8bd5" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-8f66" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-901f" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-ff1a" transform="translate(400 0)"/>
    </g>
   </g>
   <g id="text_4">
    <!-- $V_0 = 50$ km/h -->
    <g transform="translate(120.096 177.552) scale(0.18 -0.18)">
     <defs>
      <path id="SimHei-56" d="M 3050 4400 
L 1800 75 
L 1300 75 
L 50 4400 
L 650 4400 
L 1525 1125 
L 1575 1125 
L 2450 4400 
L 3050 4400 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-30" d="M 225 2537 
Q 250 3200 412 3587 
Q 575 3975 875 4225 
Q 1175 4475 1612 4475 
Q 2050 4475 2375 4112 
Q 2700 3750 2800 3200 
Q 2900 2650 2862 1937 
Q 2825 1225 2612 775 
Q 2400 325 1975 150 
Q 1550 -25 1125 187 
Q 700 400 525 750 
Q 350 1100 275 1487 
Q 200 1875 225 2537 
z
M 750 2687 
Q 675 2000 800 1462 
Q 925 925 1212 700 
Q 1500 475 1800 612 
Q 2100 750 2237 1162 
Q 2375 1575 2375 2062 
Q 2375 2550 2337 2950 
Q 2300 3350 2112 3675 
Q 1925 4000 1612 4012 
Q 1300 4025 1062 3700 
Q 825 3375 750 2687 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-3d" d="M 2975 2900 
L 125 2900 
L 125 3300 
L 2975 3300 
L 2975 2900 
z
M 2975 1375 
L 125 1375 
L 125 1775 
L 2975 1775 
L 2975 1375 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-35" d="M 550 1325 
Q 725 650 1150 575 
Q 1575 500 1837 662 
Q 2100 825 2212 1087 
Q 2325 1350 2312 1675 
Q 2300 2000 2137 2225 
Q 1975 2450 1725 2525 
Q 1475 2600 1162 2525 
Q 850 2450 650 2175 
L 225 2225 
Q 275 2375 700 4375 
L 2675 4375 
L 2675 3925 
L 1075 3925 
Q 950 3250 825 2850 
Q 1200 3025 1525 3012 
Q 1850 3000 2150 2862 
Q 2450 2725 2587 2487 
Q 2725 2250 2787 2012 
Q 2850 1775 2837 1500 
Q 2825 1225 2725 937 
Q 2625 650 2425 462 
Q 2225 275 1937 162 
Q 1650 50 1275 75 
Q 900 100 562 350 
Q 225 600 100 1200 
L 550 1325 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-20" transform="scale(0.015625)"/>
      <path id="SimHei-6b" d="M 2925 125 
L 2350 125 
L 1450 1650 
L 875 1100 
L 875 125 
L 375 125 
L 375 4400 
L 875 4400 
L 875 1675 
L 2125 2925 
L 2750 2925 
L 1775 1975 
L 2925 125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6d" d="M 3050 125 
L 2550 125 
L 2550 2150 
Q 2550 2300 2500 2400 
Q 2450 2500 2300 2500 
Q 2125 2500 1975 2312 
Q 1825 2125 1825 1825 
L 1825 125 
L 1325 125 
L 1325 2150 
Q 1325 2300 1275 2400 
Q 1225 2500 1075 2500 
Q 900 2500 750 2312 
Q 600 2125 600 1825 
L 600 125 
L 100 125 
L 100 2925 
L 600 2925 
L 600 2550 
Q 725 2750 900 2862 
Q 1075 2975 1275 2975 
Q 1475 2975 1612 2862 
Q 1750 2750 1800 2550 
Q 1925 2750 2087 2862 
Q 2250 2975 2450 2975 
Q 2750 2975 2900 2812 
Q 3050 2650 3050 2350 
L 3050 125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-2f" d="M 3000 4550 
L 350 -175 
L 100 -25 
L 2750 4700 
L 3000 4550 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-68" d="M 2800 125 
L 2300 125 
L 2300 1925 
Q 2300 2225 2150 2400 
Q 2000 2575 1750 2575 
Q 1425 2575 1137 2237 
Q 850 1900 850 1400 
L 850 125 
L 350 125 
L 350 4400 
L 850 4400 
L 850 2400 
Q 1050 2675 1287 2825 
Q 1525 2975 1900 2975 
Q 2350 2975 2575 2725 
Q 2800 2475 2800 2100 
L 2800 125 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-56" transform="translate(0 0.5625)"/>
     <use xlink:href="#SimHei-30" transform="translate(50.799805 -13.148438) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(102.524957 0.5625)"/>
     <use xlink:href="#SimHei-35" transform="translate(166.964954 0.5625)"/>
     <use xlink:href="#SimHei-30" transform="translate(216.964954 0.5625)"/>
     <use xlink:href="#SimHei-20" transform="translate(266.964954 0.5625)"/>
     <use xlink:href="#SimHei-6b" transform="translate(316.964954 0.5625)"/>
     <use xlink:href="#SimHei-6d" transform="translate(366.964954 0.5625)"/>
     <use xlink:href="#SimHei-2f" transform="translate(416.964954 0.5625)"/>
     <use xlink:href="#SimHei-68" transform="translate(466.964954 0.5625)"/>
    </g>
   </g>
   <g id="text_5">
    <!-- 系统反应时间： -->
    <g transform="translate(120.096 210) scale(0.18 -0.18)">
     <defs>
      <path id="SimHei-7cfb" d="M 5300 5200 
Q 5450 4950 5725 4675 
Q 4975 4650 4437 4587 
Q 3900 4525 3125 4450 
Q 3250 4325 3475 4225 
Q 3150 4025 2837 3762 
Q 2525 3500 2225 3275 
L 3475 3275 
Q 4100 3700 4525 4050 
Q 4775 3850 5125 3700 
Q 4775 3550 4450 3375 
Q 4125 3200 3387 2737 
Q 2650 2275 2125 1975 
Q 3600 2050 4875 2100 
Q 4750 2225 4225 2625 
Q 4475 2725 4650 2875 
Q 4950 2650 5337 2312 
Q 5725 1975 6075 1650 
Q 5775 1475 5600 1325 
Q 5400 1575 5225 1725 
Q 4550 1700 3625 1650 
L 3625 150 
Q 3625 -325 3387 -412 
Q 3150 -500 2525 -600 
Q 2475 -300 2300 -25 
Q 2875 -25 3012 12 
Q 3150 50 3150 300 
L 3150 1650 
Q 1625 1625 1075 1525 
Q 1000 1775 900 2050 
Q 1450 2100 1875 2300 
Q 2300 2500 2900 2900 
Q 2325 2900 2037 2875 
Q 1750 2850 1400 2800 
Q 1325 3075 1250 3325 
Q 1675 3325 2100 3662 
Q 2525 4000 2750 4400 
Q 1350 4350 925 4300 
Q 850 4550 750 4750 
Q 2150 4750 3250 4850 
Q 4350 4950 5300 5200 
z
M 4625 1250 
Q 4925 1025 5325 737 
Q 5725 450 6000 175 
Q 5800 0 5600 -225 
Q 5275 125 4925 400 
Q 4575 675 4275 875 
Q 4500 1050 4625 1250 
z
M 2200 875 
Q 1900 725 1537 375 
Q 1175 25 800 -300 
Q 575 -75 375 25 
Q 1075 450 1675 1225 
Q 1900 1050 2200 875 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7edf" d="M 5150 4300 
Q 5525 4300 5950 4325 
L 5950 3875 
Q 5550 3900 5175 3900 
L 3775 3900 
Q 3975 3750 4250 3600 
Q 4050 3450 3837 3162 
Q 3625 2875 3225 2375 
Q 3800 2375 5025 2500 
Q 4925 2625 4625 2975 
Q 4850 3100 5025 3250 
Q 5450 2800 5950 2100 
Q 5700 1950 5525 1775 
Q 5375 1975 5275 2125 
Q 4925 2100 4825 2075 
L 4825 200 
Q 4825 -100 5075 -100 
L 5325 -100 
Q 5550 -100 5625 575 
Q 5850 400 6150 325 
Q 5925 -525 5525 -525 
L 4850 -525 
Q 4350 -525 4350 0 
L 4350 2025 
Q 4000 2000 3875 1975 
Q 3900 1050 3637 462 
Q 3375 -125 2625 -700 
Q 2500 -500 2175 -375 
Q 2575 -125 2850 175 
Q 3125 475 3275 850 
Q 3425 1225 3400 1925 
Q 3025 1900 2725 1825 
Q 2650 2200 2525 2475 
Q 2825 2500 3187 2975 
Q 3550 3450 3675 3900 
L 3275 3900 
Q 2950 3900 2475 3875 
L 2475 4325 
Q 2875 4300 3250 4300 
L 5150 4300 
z
M 1875 4825 
Q 1700 4600 1500 4250 
Q 1300 3900 900 3250 
Q 1375 3275 1650 3275 
Q 1850 3625 1975 3975 
Q 2250 3800 2550 3675 
Q 2375 3500 2075 3037 
Q 1775 2575 1225 1725 
Q 1950 1800 2500 1900 
Q 2375 1600 2375 1425 
Q 2150 1400 1587 1325 
Q 1025 1250 600 1125 
Q 525 1375 425 1625 
Q 700 1700 950 2075 
Q 1200 2450 1450 2900 
Q 1225 2875 925 2837 
Q 625 2800 425 2750 
Q 325 3000 200 3225 
Q 500 3300 862 4025 
Q 1225 4750 1275 5075 
Q 1575 4925 1875 4825 
z
M 2550 825 
Q 2525 575 2550 350 
Q 1975 200 1412 37 
Q 850 -125 550 -250 
Q 450 0 275 275 
Q 900 375 1450 500 
Q 2000 625 2550 825 
z
M 4100 5275 
Q 4375 4825 4500 4575 
Q 4325 4500 4025 4350 
Q 3825 4825 3650 5075 
Q 3950 5175 4100 5275 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-53cd" d="M 1725 2800 
Q 1650 1600 1437 925 
Q 1225 250 775 -475 
Q 625 -325 300 -175 
Q 700 375 925 1075 
Q 1150 1775 1200 2537 
Q 1250 3300 1237 3737 
Q 1225 4175 1225 4625 
Q 1750 4600 2237 4625 
Q 2725 4650 3575 4725 
Q 4425 4800 5200 5000 
Q 5350 4700 5525 4450 
Q 4750 4400 3612 4300 
Q 2475 4200 1725 4200 
L 1725 3250 
L 5350 3250 
Q 5200 2725 4837 1975 
Q 4475 1225 3975 650 
Q 4300 400 4962 175 
Q 5625 -50 5925 -75 
Q 5675 -250 5575 -600 
Q 5175 -500 4612 -275 
Q 4050 -50 3600 325 
Q 3200 25 2662 -225 
Q 2125 -475 1475 -650 
Q 1425 -425 1175 -200 
Q 1775 -75 2350 162 
Q 2925 400 3225 650 
Q 2850 1075 2562 1587 
Q 2275 2100 2050 2800 
L 1725 2800 
z
M 2550 2800 
Q 2975 1625 3600 1000 
Q 4275 1775 4625 2800 
L 2550 2800 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5e94" d="M 3525 5275 
Q 3700 4825 3850 4400 
L 4750 4400 
Q 5275 4400 5850 4425 
L 5850 3975 
Q 5275 4000 4750 4000 
L 1475 4000 
Q 1475 2525 1425 1900 
Q 1375 1275 1237 625 
Q 1100 -25 750 -625 
Q 575 -400 300 -250 
Q 625 175 800 937 
Q 975 1700 1000 2575 
Q 1025 3450 1000 4400 
L 3250 4400 
Q 3175 4750 3000 5100 
Q 3250 5150 3525 5275 
z
M 5625 3125 
Q 5375 2575 5187 2050 
Q 5000 1525 4450 225 
L 5150 225 
Q 5500 225 5925 250 
L 5925 -275 
Q 5500 -250 5150 -250 
L 2475 -250 
Q 2050 -250 1450 -275 
L 1450 250 
Q 2025 225 2475 225 
L 3950 225 
Q 4325 1000 4612 1850 
Q 4900 2700 5025 3350 
Q 5350 3175 5625 3125 
z
M 2100 2850 
Q 2750 1825 3075 1050 
Q 2900 950 2650 800 
Q 2150 1925 1700 2575 
Q 1925 2750 2100 2850 
z
M 3300 3425 
Q 3800 2550 4175 1700 
Q 3950 1575 3700 1450 
Q 3400 2300 2900 3200 
Q 3100 3275 3300 3425 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-65f6" d="M 2525 4575 
Q 2500 3675 2500 2400 
Q 2500 1125 2525 400 
L 2000 400 
L 2000 875 
L 1000 875 
L 1000 50 
L 475 50 
Q 500 1200 500 2350 
Q 500 3525 475 4575 
L 2525 4575 
z
M 4875 3825 
Q 4875 4575 4850 5225 
L 5400 5225 
Q 5375 4575 5375 3825 
Q 5750 3825 6175 3850 
L 6175 3375 
Q 5750 3400 5375 3400 
L 5375 50 
Q 5375 -325 5075 -425 
Q 4775 -525 4300 -575 
Q 4300 -275 4025 0 
Q 4500 -50 4687 -25 
Q 4875 0 4875 300 
L 4875 3400 
Q 3625 3400 2775 3375 
L 2775 3850 
Q 3575 3825 4875 3825 
z
M 2000 1300 
L 2000 2550 
L 1000 2550 
L 1000 1300 
L 2000 1300 
z
M 2000 2975 
L 2000 4150 
L 1000 4150 
L 1000 2975 
L 2000 2975 
z
M 3550 2725 
Q 3875 2200 4175 1600 
Q 3925 1525 3650 1375 
Q 3450 1975 3075 2500 
Q 3300 2600 3550 2725 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-95f4" d="M 4450 3550 
Q 4425 3075 4425 2725 
L 4425 1400 
Q 4425 1075 4450 625 
L 1925 625 
Q 1950 1075 1950 1425 
L 1950 2725 
Q 1950 3075 1925 3550 
L 4450 3550 
z
M 5800 4875 
Q 5775 4400 5775 3925 
L 5775 250 
Q 5800 -325 5462 -450 
Q 5125 -575 4725 -625 
Q 4700 -325 4525 -25 
Q 5000 -50 5137 25 
Q 5275 100 5275 350 
L 5275 4450 
L 3775 4450 
Q 3175 4450 2650 4425 
L 2650 4900 
Q 3175 4875 3775 4875 
L 5800 4875 
z
M 550 -525 
Q 600 -25 600 525 
L 600 3150 
Q 600 3675 550 4025 
L 1125 4025 
Q 1100 3650 1100 3300 
L 1100 500 
Q 1100 0 1125 -525 
L 550 -525 
z
M 3950 1025 
L 3950 1950 
L 2425 1950 
L 2425 1025 
L 3950 1025 
z
M 3950 2350 
L 3950 3150 
L 2425 3150 
L 2425 2350 
L 3950 2350 
z
M 1525 5300 
Q 1750 5000 2225 4350 
Q 1950 4225 1700 4075 
Q 1450 4575 1075 5025 
Q 1350 5225 1525 5300 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-7cfb"/>
     <use xlink:href="#SimHei-7edf" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-53cd" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-5e94" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-65f6" transform="translate(400 0)"/>
     <use xlink:href="#SimHei-95f4" transform="translate(500 0)"/>
     <use xlink:href="#SimHei-ff1a" transform="translate(600 0)"/>
    </g>
   </g>
   <g id="text_6">
    <!-- $t = 0.5$ s -->
    <g transform="translate(120.096 234.336) scale(0.18 -0.18)">
     <defs>
      <path id="SimHei-74" d="M 2750 200 
Q 2625 150 2462 112 
Q 2300 75 2025 75 
Q 1575 75 1300 325 
Q 1025 575 1025 1025 
L 1025 2525 
L 175 2525 
L 175 2925 
L 1025 2925 
L 1025 3900 
L 1525 3900 
L 1525 2925 
L 2550 2925 
L 2550 2525 
L 1525 2525 
L 1525 1000 
Q 1525 800 1625 662 
Q 1725 525 2000 525 
Q 2275 525 2450 575 
Q 2625 625 2750 700 
L 2750 200 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-2e" d="M 1075 125 
L 500 125 
L 500 675 
L 1075 675 
L 1075 125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-73" d="M 2750 900 
Q 2750 500 2437 287 
Q 2125 75 1650 75 
Q 1050 75 725 312 
Q 400 550 400 1000 
L 900 1000 
Q 900 700 1112 600 
Q 1325 500 1625 500 
Q 1925 500 2075 612 
Q 2225 725 2225 900 
Q 2225 1025 2100 1150 
Q 1975 1275 1475 1350 
Q 900 1425 687 1637 
Q 475 1850 475 2200 
Q 475 2500 762 2737 
Q 1050 2975 1600 2975 
Q 2100 2975 2387 2750 
Q 2675 2525 2675 2150 
L 2175 2150 
Q 2175 2375 2012 2462 
Q 1850 2550 1600 2550 
Q 1275 2550 1137 2437 
Q 1000 2325 1000 2175 
Q 1000 2000 1125 1900 
Q 1250 1800 1650 1750 
Q 2300 1650 2525 1437 
Q 2750 1225 2750 900 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-74" transform="translate(0 0.078125)"/>
     <use xlink:href="#SimHei-3d" transform="translate(64.439996 0.078125)"/>
     <use xlink:href="#SimHei-30" transform="translate(128.879993 0.078125)"/>
     <use xlink:href="#SimHei-2e" transform="translate(178.879993 0.078125)"/>
     <use xlink:href="#SimHei-35" transform="translate(228.879993 0.078125)"/>
     <use xlink:href="#SimHei-20" transform="translate(278.879993 0.078125)"/>
     <use xlink:href="#SimHei-73" transform="translate(328.879993 0.078125)"/>
    </g>
   </g>
   <g id="text_7">
    <!-- 最大减速度： -->
    <g transform="translate(120.096 266.784) scale(0.18 -0.18)">
     <defs>
      <path id="SimHei-6700" d="M 475 275 
Q 675 300 1100 350 
L 1100 2450 
Q 775 2450 425 2425 
L 425 2850 
Q 775 2825 1125 2825 
L 5025 2825 
Q 5475 2825 5900 2850 
L 5900 2425 
Q 5475 2450 5025 2450 
L 3050 2450 
L 3050 675 
L 3400 775 
Q 3400 550 3425 400 
Q 3225 350 3050 325 
Q 3050 -125 3075 -550 
L 2600 -550 
Q 2625 -125 2625 250 
Q 2350 200 1812 87 
Q 1275 -25 650 -175 
Q 575 100 475 275 
z
M 5100 4925 
Q 5075 4600 5075 4325 
L 5075 3875 
Q 5075 3600 5100 3250 
L 1275 3250 
Q 1300 3600 1300 3875 
L 1300 4325 
Q 1300 4600 1275 4925 
L 5100 4925 
z
M 3300 2050 
Q 3750 2025 4125 2025 
L 5375 2025 
Q 5100 975 4725 400 
Q 5350 0 5925 -100 
Q 5700 -275 5650 -500 
Q 5025 -300 4475 150 
Q 4150 -200 3600 -575 
Q 3500 -425 3250 -275 
Q 3775 -100 4150 350 
Q 3725 775 3500 1675 
L 3300 1675 
L 3300 2050 
z
M 4600 4250 
L 4600 4600 
L 1750 4600 
L 1750 4250 
L 4600 4250 
z
M 4600 3575 
L 4600 3900 
L 1750 3900 
L 1750 3575 
L 4600 3575 
z
M 3900 1675 
Q 4000 1075 4400 625 
Q 4700 1050 4850 1675 
L 3900 1675 
z
M 2625 1250 
L 2625 1675 
L 1525 1675 
L 1525 1250 
L 2625 1250 
z
M 2625 575 
L 2625 925 
L 1525 925 
L 1525 425 
L 2625 575 
z
M 2625 2025 
L 2625 2450 
L 1525 2450 
L 1525 2025 
L 2625 2025 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5927" d="M 2900 3400 
Q 2925 4275 2912 4575 
Q 2900 4875 2875 5150 
Q 3175 5100 3550 5075 
Q 3475 4825 3437 4350 
Q 3400 3875 3400 3400 
L 4675 3400 
Q 5350 3400 5925 3450 
L 5925 2900 
Q 5425 2925 4900 2925 
L 3425 2925 
Q 3525 2500 3775 2062 
Q 4025 1625 4287 1287 
Q 4550 950 5012 575 
Q 5475 200 6025 -50 
Q 5825 -200 5625 -550 
Q 5175 -300 4737 75 
Q 4300 450 3862 1062 
Q 3425 1675 3200 2200 
Q 3000 1650 2775 1287 
Q 2550 925 2287 625 
Q 2025 325 1612 25 
Q 1200 -275 675 -575 
Q 475 -275 225 -50 
Q 750 125 1262 450 
Q 1775 775 2100 1187 
Q 2425 1600 2625 2050 
Q 2825 2500 2875 2925 
L 1500 2925 
Q 900 2925 550 2900 
L 550 3450 
Q 900 3425 1500 3400 
L 2900 3400 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-51cf" d="M 3850 4100 
Q 3825 4550 3800 5125 
Q 4075 5100 4350 5100 
Q 4300 4900 4300 4100 
L 4825 4100 
Q 5250 4100 5775 4125 
L 5775 3675 
Q 5300 3700 4850 3700 
L 4325 3700 
Q 4300 2525 4600 1525 
Q 5000 2275 5200 3175 
Q 5450 3025 5725 2950 
Q 5600 2675 5325 2050 
Q 5050 1425 4800 975 
Q 5200 200 5412 75 
Q 5625 -50 5775 700 
Q 5900 575 6200 450 
Q 6025 -100 5825 -375 
Q 5625 -650 5325 -475 
Q 5025 -300 4525 550 
Q 4025 -100 3375 -600 
Q 3200 -350 3000 -200 
Q 3650 100 4300 1050 
Q 3925 2025 3875 3700 
L 2100 3700 
L 2100 2250 
Q 2150 500 1450 -625 
Q 1250 -450 950 -350 
Q 1375 175 1525 962 
Q 1675 1750 1675 2700 
Q 1675 3650 1650 4100 
L 3850 4100 
z
M 3725 2300 
Q 3700 2000 3700 1400 
Q 3700 825 3725 600 
L 2400 600 
Q 2425 950 2425 1400 
Q 2425 1875 2400 2300 
L 3725 2300 
z
M 1600 2025 
Q 1325 1525 1050 975 
Q 775 425 650 125 
Q 400 350 175 450 
Q 475 825 712 1300 
Q 950 1775 1150 2325 
Q 1375 2150 1600 2025 
z
M 775 4450 
Q 1200 3775 1500 3275 
Q 1275 3150 1025 2950 
Q 900 3275 325 4150 
Q 575 4325 775 4450 
z
M 2375 3175 
L 3700 3175 
L 3700 2775 
L 2375 2775 
L 2375 3175 
z
M 3300 950 
L 3300 1925 
L 2825 1925 
L 2825 950 
L 3300 950 
z
M 4975 5150 
Q 5200 4925 5575 4475 
Q 5375 4325 5200 4175 
Q 4925 4575 4625 4850 
Q 4825 5025 4975 5150 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5ea6" d="M 1750 1675 
Q 2450 1650 2875 1650 
L 5200 1650 
Q 4975 1250 4650 850 
Q 4325 450 4025 200 
Q 4350 50 4950 -50 
Q 5550 -150 6100 -150 
Q 5950 -300 5825 -650 
Q 5100 -550 4550 -412 
Q 4000 -275 3625 -100 
Q 3100 -325 2587 -462 
Q 2075 -600 1525 -675 
Q 1450 -475 1225 -250 
Q 1675 -250 2250 -125 
Q 2825 0 3250 200 
Q 2825 550 2400 1275 
L 1750 1250 
L 1750 1675 
z
M 2975 5075 
Q 3250 5150 3475 5250 
Q 3625 4975 3775 4525 
L 4700 4525 
Q 4975 4525 5700 4550 
L 5700 4100 
Q 5000 4125 4650 4125 
L 1375 4125 
Q 1375 3000 1350 2312 
Q 1325 1625 1212 950 
Q 1100 275 725 -525 
Q 500 -375 250 -300 
Q 650 400 762 1075 
Q 875 1750 887 2800 
Q 900 3850 875 4525 
L 3200 4525 
Q 3100 4850 2975 5075 
z
M 2475 3300 
Q 2475 3700 2450 3925 
L 2975 3925 
Q 2950 3725 2950 3300 
L 4300 3300 
Q 4300 3700 4275 3950 
L 4800 3950 
Q 4775 3650 4775 3300 
Q 5325 3300 5725 3325 
L 5725 2900 
Q 5350 2925 4775 2925 
Q 4775 2250 4800 1950 
L 2450 1950 
Q 2475 2350 2475 2925 
Q 2175 2925 1600 2900 
L 1600 3325 
Q 2175 3300 2475 3300 
z
M 4300 2350 
L 4300 2925 
L 2950 2925 
L 2950 2350 
L 4300 2350 
z
M 2875 1275 
Q 3200 775 3650 425 
Q 4025 700 4425 1275 
L 2900 1275 
L 2875 1275 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-6700"/>
     <use xlink:href="#SimHei-5927" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-51cf" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-901f" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-5ea6" transform="translate(400 0)"/>
     <use xlink:href="#SimHei-ff1a" transform="translate(500 0)"/>
    </g>
   </g>
   <g id="text_8">
    <!-- $a = 9.8$ m/s² -->
    <g transform="translate(120.096 291.12) scale(0.18 -0.18)">
     <defs>
      <path id="SimHei-61" d="M 2875 125 
L 2275 125 
Q 2225 175 2200 262 
Q 2175 350 2175 475 
Q 2000 275 1750 175 
Q 1500 75 1225 75 
Q 825 75 550 275 
Q 275 475 275 850 
Q 275 1225 525 1450 
Q 775 1675 1300 1750 
Q 1650 1800 1912 1875 
Q 2175 1950 2175 2075 
Q 2175 2225 2062 2375 
Q 1950 2525 1575 2525 
Q 1275 2525 1137 2412 
Q 1000 2300 950 2100 
L 400 2100 
Q 450 2500 762 2737 
Q 1075 2975 1575 2975 
Q 2125 2975 2400 2725 
Q 2675 2475 2675 2025 
L 2675 650 
Q 2675 500 2725 375 
Q 2775 250 2875 125 
z
M 2175 1050 
L 2175 1550 
Q 2025 1500 1887 1462 
Q 1750 1425 1425 1375 
Q 1050 1325 937 1200 
Q 825 1075 825 900 
Q 825 750 937 637 
Q 1050 525 1275 525 
Q 1500 525 1762 650 
Q 2025 775 2175 1050 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-39" d="M 800 75 
Q 1350 1000 1700 1750 
Q 1075 1650 662 1912 
Q 250 2175 162 2625 
Q 75 3075 175 3437 
Q 275 3800 487 4037 
Q 700 4275 1000 4375 
Q 1300 4475 1537 4475 
Q 1775 4475 2025 4375 
Q 2275 4275 2475 4075 
Q 2675 3875 2750 3662 
Q 2825 3450 2825 3112 
Q 2825 2775 2575 2250 
Q 2325 1725 1350 75 
L 800 75 
z
M 662 2875 
Q 700 2550 937 2337 
Q 1175 2125 1450 2150 
Q 1725 2175 1887 2300 
Q 2050 2425 2200 2725 
Q 2300 3100 2250 3362 
Q 2200 3625 1987 3800 
Q 1775 3975 1525 3975 
Q 1375 4000 1137 3900 
Q 900 3800 762 3500 
Q 625 3200 662 2875 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-38" d="M 175 1375 
Q 175 1675 325 1962 
Q 475 2250 825 2425 
Q 525 2600 425 2812 
Q 325 3025 312 3300 
Q 300 3575 387 3775 
Q 475 3975 650 4150 
Q 825 4325 1037 4387 
Q 1250 4450 1500 4450 
Q 1750 4450 1950 4400 
Q 2150 4350 2375 4187 
Q 2600 4025 2700 3725 
Q 2800 3425 2687 3025 
Q 2575 2625 2100 2400 
Q 2525 2275 2700 2012 
Q 2875 1750 2875 1375 
Q 2875 1000 2762 775 
Q 2650 550 2512 400 
Q 2375 250 2137 162 
Q 1900 75 1537 75 
Q 1175 75 912 162 
Q 650 250 475 425 
Q 300 600 237 837 
Q 175 1075 175 1375 
z
M 687 1400 
Q 675 1100 787 875 
Q 900 650 1200 587 
Q 1500 525 1825 600 
Q 2150 675 2275 950 
Q 2400 1225 2362 1500 
Q 2325 1775 2050 1962 
Q 1775 2150 1450 2125 
Q 1125 2100 912 1900 
Q 700 1700 687 1400 
z
M 775 3350 
Q 775 3100 950 2875 
Q 1125 2650 1500 2650 
Q 1875 2650 2062 2875 
Q 2250 3100 2237 3412 
Q 2225 3725 2012 3875 
Q 1800 4025 1437 4000 
Q 1075 3975 925 3787 
Q 775 3600 775 3350 
z
" transform="scale(0.015625)"/>
      <path id="STIXGeneral-Regular-a4" d="M 3341 256 
L 3034 -64 
L 2406 563 
Q 2029 301 1606 301 
Q 1171 301 794 563 
L 179 -64 
L -141 256 
L 486 870 
Q 224 1248 224 1677 
Q 224 2118 486 2483 
L -141 3110 
L 179 3418 
L 794 2803 
Q 1184 3053 1606 3053 
Q 2048 3053 2406 2803 
L 3034 3418 
L 3341 3110 
L 2726 2483 
Q 2976 2112 2976 1683 
Q 2976 1261 2726 870 
L 3341 256 
z
M 2541 1690 
Q 2541 2086 2265 2364 
Q 1990 2643 1594 2643 
Q 1210 2643 934 2361 
Q 659 2080 659 1683 
Q 659 1280 937 995 
Q 1216 710 1606 710 
Q 2003 710 2272 995 
Q 2541 1280 2541 1690 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-61" transform="translate(0 0.5625)"/>
     <use xlink:href="#SimHei-3d" transform="translate(64.439996 0.5625)"/>
     <use xlink:href="#SimHei-39" transform="translate(128.879993 0.5625)"/>
     <use xlink:href="#SimHei-2e" transform="translate(178.879993 0.5625)"/>
     <use xlink:href="#SimHei-38" transform="translate(228.879993 0.5625)"/>
     <use xlink:href="#SimHei-20" transform="translate(278.879993 0.5625)"/>
     <use xlink:href="#SimHei-6d" transform="translate(328.879993 0.5625)"/>
     <use xlink:href="#SimHei-2f" transform="translate(378.879993 0.5625)"/>
     <use xlink:href="#SimHei-73" transform="translate(428.879993 0.5625)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(478.879993 0.5625)"/>
    </g>
   </g>
   <g id="text_9">
    <!-- 计算步骤 -->
    <g transform="translate(915.76 120.768) scale(0.22 -0.22)">
     <defs>
      <path id="SimHei-6b65" d="M 4800 4250 
Q 5150 4250 5550 4275 
L 5550 3825 
Q 5150 3850 4800 3850 
L 3700 3850 
L 3700 3000 
L 5125 3000 
Q 5675 3000 6100 3025 
L 6100 2550 
Q 5675 2575 5125 2575 
L 1475 2575 
Q 1050 2575 475 2525 
L 475 3025 
Q 1050 3000 1475 3000 
L 1725 3000 
L 1725 3775 
Q 1725 4225 1700 4500 
L 2225 4500 
Q 2200 4175 2200 3775 
L 2200 3000 
L 3225 3000 
Q 3225 4750 3200 5100 
L 3725 5100 
Q 3700 4750 3700 4250 
L 4800 4250 
z
M 3725 2400 
Q 3700 1975 3700 1600 
L 3700 450 
Q 4250 750 4725 1225 
Q 5200 1700 5400 2050 
Q 5650 1825 5900 1700 
Q 5675 1475 5362 1150 
Q 5050 825 4537 437 
Q 4025 50 3212 -225 
Q 2400 -500 800 -650 
Q 725 -400 500 -150 
Q 1275 -150 2100 -25 
Q 2925 100 3225 275 
L 3225 1600 
Q 3225 1975 3200 2400 
L 3725 2400 
z
M 2600 1875 
Q 2275 1600 1975 1275 
Q 1675 950 1075 400 
Q 875 600 700 725 
Q 1175 1075 1562 1512 
Q 1950 1950 2125 2200 
Q 2325 2025 2600 1875 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-9aa4" d="M 5825 2325 
Q 5650 2575 5375 2850 
Q 5300 2750 5175 2612 
Q 5050 2475 4775 2225 
Q 4600 2375 4425 2475 
Q 4675 2650 4800 2775 
Q 4925 2900 5100 3125 
Q 4875 3400 4500 3675 
Q 4600 3800 4725 3975 
Q 5100 3650 5325 3450 
Q 5450 3725 5550 4250 
L 5300 4250 
Q 5050 4250 4700 4225 
L 4700 4525 
L 4300 4525 
L 4300 2925 
L 4525 2975 
L 4525 2650 
L 4300 2600 
L 4300 2225 
L 3875 2225 
L 3875 2525 
Q 3550 2450 3175 2350 
Q 2800 2250 2475 2125 
L 2250 2575 
Q 2650 2625 2775 2650 
L 2775 4525 
Q 2525 4525 2250 4500 
L 2250 4900 
Q 2550 4875 2925 4875 
L 4225 4875 
Q 4575 4875 4875 4900 
L 4875 4600 
L 6075 4600 
Q 5975 4100 5887 3812 
Q 5800 3525 5600 3175 
Q 5825 3000 6175 2625 
Q 6000 2500 5825 2325 
z
M 1425 2200 
L 1575 4350 
L 1125 4350 
Q 750 4350 375 4325 
L 375 4750 
Q 750 4725 1100 4725 
L 2050 4725 
Q 2000 4425 1975 4025 
L 1850 2200 
L 2225 2200 
Q 2175 1850 2175 1650 
L 2100 125 
Q 2075 -300 1825 -375 
Q 1575 -450 1225 -525 
Q 1175 -275 1025 0 
Q 1450 0 1562 50 
Q 1675 100 1700 375 
L 1750 1825 
L 400 1825 
Q 425 2025 462 2537 
Q 500 3050 550 3950 
L 1025 3925 
Q 950 3450 925 3037 
Q 900 2625 875 2200 
L 1425 2200 
z
M 4150 1600 
Q 3800 1550 3575 1525 
Q 3350 1500 2825 1425 
Q 2750 1650 2625 1800 
Q 3275 1825 3625 1875 
Q 3975 1925 4300 1975 
Q 4625 2025 4875 2075 
Q 5125 2125 5375 2225 
Q 5475 2050 5675 1800 
Q 5400 1800 5187 1762 
Q 4975 1725 4600 1675 
Q 4700 1275 4925 1025 
Q 5100 1150 5212 1287 
Q 5325 1425 5500 1650 
Q 5700 1475 5925 1275 
Q 5675 1150 5512 1037 
Q 5350 925 5175 750 
Q 5525 350 6100 150 
Q 5900 0 5725 -275 
Q 5375 -25 5075 262 
Q 4775 550 4575 950 
L 4575 200 
Q 4575 -300 4600 -625 
L 4125 -625 
Q 4150 -300 4150 200 
L 4150 675 
Q 3850 350 3462 -50 
Q 3075 -450 2700 -700 
Q 2525 -450 2325 -325 
Q 2825 -125 3212 262 
Q 3600 650 3875 1100 
Q 3975 975 4150 850 
L 4150 1600 
z
M 2225 525 
Q 2500 650 2737 850 
Q 2975 1050 3225 1375 
Q 3375 1200 3550 1050 
Q 3300 850 3062 625 
Q 2825 400 2550 175 
Q 2375 375 2225 525 
z
M 1425 925 
Q 1200 850 875 700 
Q 550 550 400 450 
Q 300 750 200 925 
Q 525 1025 837 1137 
Q 1150 1250 1425 1375 
L 1425 925 
z
M 3875 3375 
L 3875 3775 
L 3175 3775 
L 3175 3375 
L 3875 3375 
z
M 3875 4125 
L 3875 4525 
L 3175 4525 
L 3175 4125 
L 3875 4125 
z
M 3875 2850 
L 3875 3050 
L 3175 3050 
L 3175 2725 
L 3875 2850 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-8ba1"/>
     <use xlink:href="#SimHei-7b97" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-6b65" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-9aa4" transform="translate(300 0)"/>
    </g>
   </g>
   <g id="text_10">
    <!-- 步骤一：单位换算 -->
    <g transform="translate(599.904 153.216) scale(0.18 -0.18)">
     <defs>
      <path id="SimHei-4e00" d="M 4875 2675 
Q 5525 2675 6050 2700 
L 6050 2050 
Q 5525 2075 4900 2075 
L 1600 2075 
Q 950 2075 350 2050 
L 350 2700 
Q 950 2675 1600 2675 
L 4875 2675 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5355" d="M 5275 4075 
L 5275 1525 
L 3400 1525 
L 3400 925 
L 5050 925 
Q 5600 925 6000 950 
L 6000 450 
Q 5575 475 5050 475 
L 3400 475 
Q 3400 -300 3425 -625 
L 2875 -625 
Q 2925 -300 2925 475 
L 1300 475 
Q 700 475 425 450 
L 425 950 
Q 675 925 1250 925 
L 2925 925 
L 2925 1525 
L 1125 1525 
Q 1150 1950 1150 2750 
Q 1150 3550 1125 4075 
L 3600 4075 
Q 3875 4650 4075 5250 
Q 4325 5125 4650 4975 
Q 4550 4850 4400 4587 
Q 4250 4325 4100 4075 
L 5275 4075 
z
M 4775 1925 
L 4775 2600 
L 3400 2600 
L 3400 1925 
L 4775 1925 
z
M 4775 3000 
L 4775 3650 
L 3400 3650 
L 3400 3000 
L 4775 3000 
z
M 2925 1925 
L 2925 2600 
L 1625 2600 
L 1625 1925 
L 2925 1925 
z
M 2925 3000 
L 2925 3650 
L 1625 3650 
L 1625 3000 
L 2925 3000 
z
M 2100 5225 
Q 2350 4950 2675 4350 
Q 2400 4225 2225 4125 
Q 1975 4675 1725 4975 
Q 1900 5075 2100 5225 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-4f4d" d="M 2125 4925 
Q 1950 4750 1625 3750 
L 1625 575 
Q 1625 -225 1650 -650 
L 1100 -650 
Q 1150 -200 1150 550 
L 1150 2950 
Q 775 2275 550 1950 
Q 375 2275 200 2425 
Q 525 2775 912 3575 
Q 1300 4375 1500 5200 
Q 1825 5000 2125 4925 
z
M 5375 3025 
Q 5275 2875 5100 2250 
Q 4925 1625 4600 300 
L 5450 300 
Q 5925 300 6100 325 
L 6100 -175 
Q 5875 -150 5450 -150 
L 2900 -150 
Q 2275 -150 2050 -175 
L 2050 325 
Q 2275 300 2850 300 
L 4075 300 
Q 4400 1400 4700 3175 
Q 5100 3075 5375 3025 
z
M 5250 4025 
Q 5700 4025 5925 4050 
L 5925 3575 
Q 5700 3600 5225 3600 
L 3000 3600 
Q 2650 3600 2275 3575 
L 2275 4050 
Q 2675 4025 3000 4025 
L 5250 4025 
z
M 3050 3050 
Q 3500 1775 3750 725 
Q 3450 625 3250 550 
Q 3125 1125 2962 1675 
Q 2800 2225 2550 2875 
Q 2800 2925 3050 3050 
z
M 3875 5200 
Q 4175 4675 4400 4300 
Q 4200 4250 3875 4075 
Q 3700 4525 3400 4950 
Q 3650 5050 3875 5200 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6362" d="M 1225 4550 
Q 1225 4900 1200 5200 
L 1700 5200 
Q 1650 4925 1650 4550 
L 1650 3800 
Q 1925 3800 2300 3825 
L 2300 3425 
Q 2725 3850 3000 4300 
Q 3275 4750 3425 5200 
Q 3625 5075 3950 4950 
Q 3800 4800 3600 4450 
L 5325 4450 
Q 4825 3750 4575 3325 
L 5600 3325 
L 5600 1650 
Q 5875 1650 6150 1675 
L 6150 1250 
Q 5725 1275 5450 1275 
L 4500 1275 
Q 4725 700 5237 325 
Q 5750 -50 6150 -150 
Q 5875 -375 5775 -625 
Q 5375 -450 4925 -37 
Q 4475 375 4150 1000 
Q 3775 50 2250 -625 
Q 2175 -425 1925 -200 
Q 2650 0 3137 425 
Q 3625 850 3775 1275 
L 2650 1275 
Q 2500 1275 2100 1250 
L 2100 1675 
Q 2475 1650 2675 1650 
L 2675 2675 
Q 2675 3050 2650 3150 
L 2550 3025 
Q 2400 3200 2175 3325 
L 2275 3400 
Q 1925 3425 1650 3425 
L 1650 2350 
Q 1925 2475 2200 2650 
Q 2225 2450 2275 2225 
Q 2025 2100 1650 1900 
L 1650 50 
Q 1650 -275 1462 -400 
Q 1275 -525 725 -600 
Q 700 -375 525 -75 
Q 1050 -100 1137 -50 
Q 1225 0 1225 150 
L 1225 1700 
Q 800 1475 525 1300 
Q 425 1525 250 1750 
Q 875 1975 1225 2150 
L 1225 3425 
Q 675 3425 300 3400 
L 300 3825 
Q 650 3800 1225 3800 
L 1225 4550 
z
M 5175 1650 
L 5175 2950 
L 4375 2950 
Q 4375 2300 4325 1650 
L 5175 1650 
z
M 3850 1650 
Q 3900 2300 3925 2950 
L 3100 2950 
L 3100 1650 
L 3850 1650 
z
M 3400 4100 
Q 3075 3625 2800 3325 
L 4100 3325 
L 4550 4100 
L 3400 4100 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-6b65"/>
     <use xlink:href="#SimHei-9aa4" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-4e00" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-ff1a" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-5355" transform="translate(400 0)"/>
     <use xlink:href="#SimHei-4f4d" transform="translate(500 0)"/>
     <use xlink:href="#SimHei-6362" transform="translate(600 0)"/>
     <use xlink:href="#SimHei-7b97" transform="translate(700 0)"/>
    </g>
   </g>
   <g id="text_11">
    <!-- $V_0 = 50 \times \frac{1000}{3600} = 13.89$ m/s -->
    <g style="fill: #d32f2f" transform="translate(628.128 177.552) scale(0.16 -0.16)">
     <defs>
      <path id="SimHei-d7" d="M 1450 4375 
L 3150 2625 
L 4875 4375 
L 5125 4050 
L 3450 2325 
L 5125 600 
L 4875 300 
L 3150 2050 
L 1475 300 
L 1450 300 
L 1200 600 
L 2900 2350 
L 1200 4075 
L 1450 4375 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-31" d="M 1400 3600 
Q 1075 3275 575 2975 
L 575 3450 
Q 1200 3875 1600 4450 
L 1900 4450 
L 1900 150 
L 1400 150 
L 1400 3600 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-33" d="M 250 1225 
L 700 1300 
Q 800 975 1025 762 
Q 1250 550 1587 562 
Q 1925 575 2125 837 
Q 2325 1100 2300 1437 
Q 2275 1775 2037 1962 
Q 1800 2150 1275 2225 
L 1275 2550 
Q 1800 2600 2037 2825 
Q 2275 3050 2250 3412 
Q 2225 3775 1925 3937 
Q 1625 4100 1287 3975 
Q 950 3850 750 3275 
L 300 3350 
Q 450 3800 712 4100 
Q 975 4400 1425 4450 
Q 1875 4500 2212 4337 
Q 2550 4175 2687 3837 
Q 2825 3500 2725 3100 
Q 2625 2700 2150 2400 
Q 2500 2250 2687 1950 
Q 2875 1650 2812 1162 
Q 2750 675 2375 375 
Q 2000 75 1525 87 
Q 1050 100 700 387 
Q 350 675 250 1225 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-36" d="M 250 1612 
Q 275 1975 387 2225 
Q 500 2475 725 2850 
L 1750 4450 
L 2325 4450 
L 1275 2800 
Q 1950 2975 2350 2750 
Q 2750 2525 2887 2237 
Q 3025 1950 3037 1612 
Q 3050 1275 2937 950 
Q 2825 625 2537 362 
Q 2250 100 1737 75 
Q 1225 50 862 262 
Q 500 475 362 862 
Q 225 1250 250 1612 
z
M 1025 787 
Q 1250 550 1625 525 
Q 2000 500 2250 775 
Q 2500 1050 2500 1575 
Q 2500 2100 2187 2300 
Q 1875 2500 1487 2450 
Q 1100 2400 925 2075 
Q 750 1750 775 1387 
Q 800 1025 1025 787 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-56" transform="translate(0 0.757812)"/>
     <use xlink:href="#SimHei-30" transform="translate(50.799805 -12.953125) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(102.524957 0.757812)"/>
     <use xlink:href="#SimHei-35" transform="translate(166.964954 0.757812)"/>
     <use xlink:href="#SimHei-30" transform="translate(216.964954 0.757812)"/>
     <use xlink:href="#SimHei-d7" transform="translate(281.40495 0.757812)"/>
     <use xlink:href="#SimHei-31" transform="translate(395.844946 50.054688) scale(0.7)"/>
     <use xlink:href="#SimHei-30" transform="translate(430.844946 50.054688) scale(0.7)"/>
     <use xlink:href="#SimHei-30" transform="translate(465.844946 50.054688) scale(0.7)"/>
     <use xlink:href="#SimHei-30" transform="translate(500.844946 50.054688) scale(0.7)"/>
     <use xlink:href="#SimHei-33" transform="translate(395.844946 -30.6875) scale(0.7)"/>
     <use xlink:href="#SimHei-36" transform="translate(430.844946 -30.6875) scale(0.7)"/>
     <use xlink:href="#SimHei-30" transform="translate(465.844946 -30.6875) scale(0.7)"/>
     <use xlink:href="#SimHei-30" transform="translate(500.844946 -30.6875) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(562.784943 0.757812)"/>
     <use xlink:href="#SimHei-31" transform="translate(627.224939 0.757812)"/>
     <use xlink:href="#SimHei-33" transform="translate(677.224939 0.757812)"/>
     <use xlink:href="#SimHei-2e" transform="translate(727.224939 0.757812)"/>
     <use xlink:href="#SimHei-38" transform="translate(777.224939 0.757812)"/>
     <use xlink:href="#SimHei-39" transform="translate(827.224939 0.757812)"/>
     <use xlink:href="#SimHei-20" transform="translate(877.224939 0.757812)"/>
     <use xlink:href="#SimHei-6d" transform="translate(927.224939 0.757812)"/>
     <use xlink:href="#SimHei-2f" transform="translate(977.224939 0.757812)"/>
     <use xlink:href="#SimHei-73" transform="translate(1027.224939 0.757812)"/>
     <path d="M 395.844946 24.78125 
L 395.844946 31.03125 
L 535.844946 31.03125 
L 535.844946 24.78125 
L 395.844946 24.78125 
z
"/>
    </g>
   </g>
   <g id="text_12">
    <!-- 步骤二：计算反应距离 -->
    <g transform="translate(599.904 210) scale(0.18 -0.18)">
     <defs>
      <path id="SimHei-4e8c" d="M 5050 625 
Q 5650 625 6025 675 
L 6025 50 
Q 5625 75 5050 75 
L 1425 75 
Q 775 75 350 50 
L 350 650 
Q 800 625 1400 625 
L 5050 625 
z
M 4350 4150 
Q 4900 4175 5350 4200 
L 5350 3575 
Q 4900 3600 4375 3600 
L 2050 3600 
Q 1450 3600 1000 3575 
L 1000 4175 
Q 1450 4150 2025 4150 
L 4350 4150 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-6b65"/>
     <use xlink:href="#SimHei-9aa4" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-4e8c" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-ff1a" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-8ba1" transform="translate(400 0)"/>
     <use xlink:href="#SimHei-7b97" transform="translate(500 0)"/>
     <use xlink:href="#SimHei-53cd" transform="translate(600 0)"/>
     <use xlink:href="#SimHei-5e94" transform="translate(700 0)"/>
     <use xlink:href="#SimHei-8ddd" transform="translate(800 0)"/>
     <use xlink:href="#SimHei-79bb" transform="translate(900 0)"/>
    </g>
   </g>
   <g id="text_13">
    <!-- $D_{\mathrm{反应}} = V_0 \times t = 13.89 \times 0.5 = 6.945$ m -->
    <g style="fill: #d32f2f" transform="translate(628.128 234.336) scale(0.16 -0.16)">
     <defs>
      <path id="SimHei-44" d="M 2925 2250 
Q 2925 1100 2437 612 
Q 1950 125 1125 125 
L 275 125 
L 275 4400 
L 1125 4400 
Q 2050 4400 2487 3900 
Q 2925 3400 2925 2250 
z
M 2325 2250 
Q 2325 3175 2025 3550 
Q 1725 3925 1125 3925 
L 850 3925 
L 850 600 
L 1125 600 
Q 1725 600 2025 962 
Q 2325 1325 2325 2250 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-34" d="M 2000 1100 
L 75 1100 
L 75 1525 
L 2100 4450 
L 2475 4450 
L 2475 1525 
L 3075 1525 
L 3075 1100 
L 2475 1100 
L 2475 150 
L 2000 150 
L 2000 1100 
z
M 2000 1525 
L 2000 3500 
L 600 1525 
L 2000 1525 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-44" transform="translate(0 0.6875)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(50.799805 -13.023438) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(85.799794 -13.023438) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(137.524936 0.6875)"/>
     <use xlink:href="#SimHei-56" transform="translate(201.964932 0.6875)"/>
     <use xlink:href="#SimHei-30" transform="translate(252.764737 -13.023438) scale(0.7)"/>
     <use xlink:href="#SimHei-d7" transform="translate(304.48989 0.6875)"/>
     <use xlink:href="#SimHei-74" transform="translate(418.929886 0.6875)"/>
     <use xlink:href="#SimHei-3d" transform="translate(483.369882 0.6875)"/>
     <use xlink:href="#SimHei-31" transform="translate(547.809879 0.6875)"/>
     <use xlink:href="#SimHei-33" transform="translate(597.809879 0.6875)"/>
     <use xlink:href="#SimHei-2e" transform="translate(647.809879 0.6875)"/>
     <use xlink:href="#SimHei-38" transform="translate(697.809879 0.6875)"/>
     <use xlink:href="#SimHei-39" transform="translate(747.809879 0.6875)"/>
     <use xlink:href="#SimHei-d7" transform="translate(812.249875 0.6875)"/>
     <use xlink:href="#SimHei-30" transform="translate(926.689871 0.6875)"/>
     <use xlink:href="#SimHei-2e" transform="translate(976.689871 0.6875)"/>
     <use xlink:href="#SimHei-35" transform="translate(1026.689871 0.6875)"/>
     <use xlink:href="#SimHei-3d" transform="translate(1091.129868 0.6875)"/>
     <use xlink:href="#SimHei-36" transform="translate(1155.569864 0.6875)"/>
     <use xlink:href="#SimHei-2e" transform="translate(1205.569864 0.6875)"/>
     <use xlink:href="#SimHei-39" transform="translate(1255.569864 0.6875)"/>
     <use xlink:href="#SimHei-34" transform="translate(1305.569864 0.6875)"/>
     <use xlink:href="#SimHei-35" transform="translate(1355.569864 0.6875)"/>
     <use xlink:href="#SimHei-20" transform="translate(1405.569864 0.6875)"/>
     <use xlink:href="#SimHei-6d" transform="translate(1455.569864 0.6875)"/>
    </g>
   </g>
   <g id="text_14">
    <!-- 步骤三：计算纯制动距离 -->
    <g transform="translate(599.904 266.784) scale(0.18 -0.18)">
     <defs>
      <path id="SimHei-4e09" d="M 6000 -200 
Q 5550 -175 5200 -175 
L 1300 -175 
Q 900 -175 375 -200 
L 375 350 
Q 850 325 1300 325 
L 5200 325 
Q 5575 325 6000 375 
L 6000 -200 
z
M 900 4600 
Q 1400 4575 1825 4575 
L 4600 4575 
Q 5075 4575 5475 4600 
L 5475 4075 
Q 5100 4100 4600 4100 
L 1800 4100 
Q 1375 4100 900 4075 
L 900 4600 
z
M 2075 2100 
Q 1700 2100 1275 2075 
L 1275 2625 
Q 1675 2600 2075 2600 
L 4225 2600 
Q 4600 2600 5050 2625 
L 5050 2075 
Q 4550 2100 4225 2100 
L 2075 2100 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7eaf" d="M 3900 4125 
Q 3900 4575 3875 5100 
L 4375 5100 
Q 4350 4650 4350 4125 
L 5025 4125 
Q 5425 4125 5850 4150 
L 5850 3725 
Q 5450 3750 5050 3750 
L 4350 3750 
L 4350 1725 
L 5175 1725 
L 5175 2425 
Q 5175 2800 5150 3100 
L 5675 3100 
Q 5625 2825 5625 2050 
Q 5625 1300 5650 1025 
L 5175 1025 
L 5175 1325 
L 4350 1325 
L 4350 250 
Q 4350 -50 4650 -50 
L 5200 -50 
Q 5350 -50 5425 25 
Q 5500 100 5575 650 
Q 5800 525 6100 450 
Q 5975 -50 5862 -237 
Q 5750 -425 5325 -425 
L 4550 -425 
Q 3900 -425 3900 0 
L 3900 1325 
L 2625 1325 
Q 2650 1625 2650 2200 
Q 2650 2775 2625 3100 
L 3150 3100 
Q 3125 2750 3125 2500 
L 3125 1725 
L 3900 1725 
L 3900 3750 
L 3350 3750 
Q 2825 3750 2425 3725 
L 2425 4150 
Q 2825 4125 3350 4125 
L 3900 4125 
z
M 2300 1300 
Q 2200 1300 1662 1250 
Q 1125 1200 575 1075 
Q 500 1300 400 1575 
Q 600 1625 887 1962 
Q 1175 2300 1525 2800 
Q 1275 2800 962 2762 
Q 650 2725 425 2625 
Q 375 2800 225 3100 
Q 450 3150 775 3737 
Q 1100 4325 1300 5050 
Q 1550 4900 1875 4775 
Q 1650 4550 1412 4125 
Q 1175 3700 825 3125 
L 1725 3150 
Q 1900 3400 2025 3725 
Q 2250 3550 2575 3425 
Q 2325 3275 2025 2837 
Q 1725 2400 1075 1625 
Q 1975 1675 2300 1725 
L 2300 1300 
z
M 2475 250 
Q 2025 175 1450 12 
Q 875 -150 450 -300 
Q 325 25 225 200 
Q 925 325 1212 387 
Q 1500 450 2475 700 
L 2475 250 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-6b65"/>
     <use xlink:href="#SimHei-9aa4" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-4e09" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-ff1a" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-8ba1" transform="translate(400 0)"/>
     <use xlink:href="#SimHei-7b97" transform="translate(500 0)"/>
     <use xlink:href="#SimHei-7eaf" transform="translate(600 0)"/>
     <use xlink:href="#SimHei-5236" transform="translate(700 0)"/>
     <use xlink:href="#SimHei-52a8" transform="translate(800 0)"/>
     <use xlink:href="#SimHei-8ddd" transform="translate(900 0)"/>
     <use xlink:href="#SimHei-79bb" transform="translate(1000 0)"/>
    </g>
   </g>
   <g id="text_15">
    <!-- $D_{\mathrm{制动}} = \frac{V_0^2}{2a} = \frac{13.89^2}{2 \times 9.8} = 9.84$ m -->
    <g style="fill: #d32f2f" transform="translate(628.128 291.12) scale(0.16 -0.16)">
     <defs>
      <path id="SimHei-32" d="M 300 250 
Q 325 625 650 925 
Q 975 1225 1475 1862 
Q 1975 2500 2125 2850 
Q 2275 3200 2237 3450 
Q 2200 3700 2000 3862 
Q 1800 4025 1537 4000 
Q 1275 3975 1037 3800 
Q 800 3625 675 3275 
L 200 3350 
Q 400 3925 712 4187 
Q 1025 4450 1450 4475 
Q 1700 4500 1900 4462 
Q 2100 4425 2312 4287 
Q 2525 4150 2662 3875 
Q 2800 3600 2762 3212 
Q 2725 2825 2375 2287 
Q 2025 1750 1025 600 
L 2825 600 
L 2825 150 
L 300 150 
L 300 250 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-44" transform="translate(0 0.320312)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(50.799805 -13.390625) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(85.799794 -13.390625) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(137.524936 0.320312)"/>
     <use xlink:href="#SimHei-56" transform="translate(205.964932 65.53125) scale(0.7)"/>
     <use xlink:href="#SimHei-32" transform="translate(241.524796 92.546875) scale(0.49)"/>
     <use xlink:href="#SimHei-30" transform="translate(241.524796 49.535156) scale(0.49)"/>
     <use xlink:href="#SimHei-32" transform="translate(201.964932 -31.125) scale(0.7)"/>
     <use xlink:href="#SimHei-61" transform="translate(236.964932 -31.125) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(298.904929 0.320312)"/>
     <use xlink:href="#SimHei-31" transform="translate(377.344925 49.34375) scale(0.7)"/>
     <use xlink:href="#SimHei-33" transform="translate(412.344925 49.34375) scale(0.7)"/>
     <use xlink:href="#SimHei-2e" transform="translate(447.344925 49.34375) scale(0.7)"/>
     <use xlink:href="#SimHei-38" transform="translate(482.344925 49.34375) scale(0.7)"/>
     <use xlink:href="#SimHei-39" transform="translate(517.344925 49.34375) scale(0.7)"/>
     <use xlink:href="#SimHei-32" transform="translate(552.904788 71.738281) scale(0.49)"/>
     <use xlink:href="#SimHei-32" transform="translate(363.344925 -31.125) scale(0.7)"/>
     <use xlink:href="#SimHei-d7" transform="translate(408.452922 -31.125) scale(0.7)"/>
     <use xlink:href="#SimHei-39" transform="translate(488.56092 -31.125) scale(0.7)"/>
     <use xlink:href="#SimHei-2e" transform="translate(523.56092 -31.125) scale(0.7)"/>
     <use xlink:href="#SimHei-38" transform="translate(558.56092 -31.125) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(620.500916 0.320312)"/>
     <use xlink:href="#SimHei-39" transform="translate(684.940912 0.320312)"/>
     <use xlink:href="#SimHei-2e" transform="translate(734.940912 0.320312)"/>
     <use xlink:href="#SimHei-38" transform="translate(784.940912 0.320312)"/>
     <use xlink:href="#SimHei-34" transform="translate(834.940912 0.320312)"/>
     <use xlink:href="#SimHei-20" transform="translate(884.940912 0.320312)"/>
     <use xlink:href="#SimHei-6d" transform="translate(934.940912 0.320312)"/>
     <path d="M 201.964932 24.34375 
L 201.964932 30.59375 
L 271.964932 30.59375 
L 271.964932 24.34375 
L 201.964932 24.34375 
z
"/>
     <path d="M 363.344925 24.34375 
L 363.344925 30.59375 
L 593.56092 30.59375 
L 593.56092 24.34375 
L 363.344925 24.34375 
z
"/>
    </g>
   </g>
   <g id="text_16">
    <!-- 关键公式 -->
    <g transform="translate(1480.24 120.768) scale(0.22 -0.22)">
     <defs>
      <path id="SimHei-5173" d="M 4675 4850 
Q 4525 4675 4362 4375 
Q 4200 4075 4075 3825 
L 4625 3825 
Q 5150 3825 5550 3850 
L 5550 3375 
Q 5225 3400 4700 3400 
L 3400 3400 
L 3400 2150 
L 4975 2150 
Q 5475 2150 5875 2175 
L 5875 1700 
Q 5475 1725 5025 1725 
L 3500 1725 
Q 4250 450 6000 75 
Q 5775 -175 5600 -525 
Q 4025 0 3225 1300 
Q 2800 200 875 -600 
Q 775 -350 500 -150 
Q 2425 375 2875 1725 
L 1550 1725 
Q 975 1725 600 1700 
L 600 2150 
Q 875 2125 1450 2125 
L 2925 2125 
L 2925 3400 
L 1750 3400 
Q 1275 3400 900 3375 
L 900 3850 
Q 1275 3825 1775 3825 
L 3575 3825 
Q 3925 4625 4075 5150 
Q 4325 5000 4675 4850 
z
M 2125 5125 
Q 2450 4725 2800 4175 
Q 2500 4025 2325 3875 
Q 2025 4450 1725 4825 
Q 1950 4975 2125 5125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-952e" d="M 5675 3750 
Q 5825 3750 6075 3775 
L 6075 3325 
Q 5825 3350 5675 3350 
Q 5675 2650 5700 2450 
L 4700 2450 
L 4700 1975 
L 5200 1975 
Q 5400 1975 5650 2000 
L 5650 1575 
Q 5550 1600 5225 1600 
L 4700 1600 
L 4700 1075 
L 5375 1075 
Q 5575 1100 5825 1100 
L 5825 675 
Q 5550 700 5350 700 
L 4700 700 
L 4700 -50 
Q 5425 -100 6150 0 
Q 6025 -225 6000 -500 
Q 5125 -525 4562 -462 
Q 4000 -400 3637 -300 
Q 3275 -200 2875 175 
Q 2650 -250 2225 -625 
Q 2025 -425 1850 -275 
Q 2275 -25 2575 450 
Q 2250 950 2125 1700 
Q 2350 1750 2550 1800 
Q 2600 1325 2750 925 
Q 2900 1650 2925 2400 
Q 2500 2400 2250 2275 
Q 2150 2500 2075 2675 
Q 2300 2775 2462 3212 
Q 2625 3650 2750 4200 
Q 2475 4200 2125 4175 
L 2125 4625 
Q 2450 4600 2675 4600 
L 3300 4600 
Q 3225 4300 3075 3837 
Q 2925 3375 2750 2775 
L 3375 2775 
Q 3325 2275 3275 1725 
Q 3225 1175 3025 525 
Q 3375 225 3650 137 
Q 3925 50 4275 -25 
L 4275 700 
Q 3875 700 3425 675 
L 3425 1100 
Q 3925 1075 4275 1075 
L 4275 1600 
Q 3950 1600 3550 1575 
L 3550 2000 
Q 3950 1975 4275 1975 
L 4275 2450 
Q 3900 2450 3650 2400 
L 3650 2850 
Q 3950 2825 4275 2825 
L 4275 3375 
L 3725 3375 
Q 3525 3375 3350 3350 
L 3350 3775 
Q 3550 3750 3725 3750 
L 4275 3750 
L 4275 4250 
Q 4000 4250 3625 4225 
L 3625 4650 
Q 3950 4625 4300 4625 
Q 4300 5025 4275 5200 
L 4725 5200 
Q 4700 5000 4700 4625 
L 5725 4625 
Q 5675 4375 5675 3750 
z
M 2200 600 
Q 1925 400 1662 162 
Q 1400 -75 1125 -375 
Q 975 -200 775 0 
Q 975 200 1000 400 
L 1000 1625 
Q 825 1625 450 1600 
L 450 2025 
Q 800 2000 1000 2000 
L 1000 2800 
Q 875 2800 700 2775 
L 700 3200 
Q 1025 3175 1325 3175 
Q 1650 3175 2050 3225 
L 2050 2800 
Q 1625 2825 1400 2825 
L 1400 2025 
Q 1700 2025 1975 2050 
L 1975 1625 
Q 1650 1650 1400 1650 
L 1400 525 
Q 1750 800 1900 1000 
Q 1975 850 2200 600 
z
M 1025 3875 
Q 825 3575 500 3175 
Q 350 3350 175 3525 
Q 400 3700 675 4162 
Q 950 4625 1150 5200 
Q 1300 5125 1650 4975 
Q 1450 4650 1250 4275 
Q 1675 4275 2000 4300 
L 2000 3850 
Q 1625 3875 1025 3875 
z
M 5250 2825 
L 5250 3375 
L 4700 3375 
L 4700 2825 
L 5250 2825 
z
M 5250 3750 
L 5250 4250 
L 4700 4250 
L 4700 3750 
L 5250 3750 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-516c" d="M 4450 550 
Q 4250 900 3900 1350 
Q 4150 1500 4325 1650 
Q 4700 1100 5025 562 
Q 5350 25 5525 -300 
Q 5250 -450 5050 -575 
Q 4900 -275 4650 150 
Q 2975 -75 2287 -175 
Q 1600 -275 1300 -400 
Q 1225 -150 1025 150 
Q 1275 250 1500 500 
Q 1725 750 2162 1550 
Q 2600 2350 2775 2950 
Q 3150 2725 3350 2675 
Q 3075 2250 2612 1400 
Q 2150 550 1950 300 
Q 3100 400 4450 550 
z
M 4025 5200 
Q 4500 3925 5037 3275 
Q 5575 2625 6125 2325 
Q 5775 2150 5650 1900 
Q 4900 2525 4375 3350 
Q 3850 4175 3525 5025 
Q 3800 5075 4025 5200 
z
M 2725 4775 
Q 2250 3775 1750 3025 
Q 1250 2275 725 1775 
Q 525 2050 300 2175 
Q 825 2575 1400 3400 
Q 1975 4225 2225 5000 
Q 2400 4875 2725 4775 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5f0f" d="M 3300 3900 
Q 3250 4775 3225 5225 
Q 3375 5200 3875 5175 
Q 3825 5000 3850 3900 
L 4850 3900 
Q 5325 3900 5825 3925 
L 5825 3425 
Q 5375 3450 4875 3450 
L 3875 3450 
Q 3950 2800 4087 2237 
Q 4225 1675 4450 1175 
Q 4675 675 5037 287 
Q 5400 -100 5475 250 
Q 5550 600 5525 950 
Q 5750 775 6150 625 
Q 5975 -450 5637 -525 
Q 5300 -600 4962 -362 
Q 4625 -125 4325 325 
Q 4025 775 3837 1312 
Q 3650 1850 3525 2425 
Q 3400 3000 3350 3450 
L 1525 3450 
Q 800 3450 350 3425 
L 350 3925 
Q 825 3900 1500 3900 
L 3300 3900 
z
M 2250 2575 
Q 2725 2575 3150 2600 
L 3150 2125 
Q 2700 2150 2200 2150 
L 2200 575 
Q 2725 700 3400 875 
L 3400 425 
Q 2500 250 1975 87 
Q 1450 -75 700 -375 
L 450 225 
Q 750 250 1700 450 
L 1700 2150 
Q 975 2150 700 2125 
L 700 2600 
Q 975 2575 1475 2575 
L 2250 2575 
z
M 4700 5075 
Q 5175 4550 5375 4300 
Q 5250 4250 4900 4000 
Q 4725 4300 4275 4750 
Q 4400 4850 4700 5075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-5173"/>
     <use xlink:href="#SimHei-952e" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-516c" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-5f0f" transform="translate(300 0)"/>
    </g>
   </g>
   <g id="text_17">
    <!-- 单位换算： -->
    <g transform="translate(1446.624 153.216) scale(0.18 -0.18)">
     <use xlink:href="#SimHei-5355"/>
     <use xlink:href="#SimHei-4f4d" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-6362" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-7b97" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-ff1a" transform="translate(400 0)"/>
    </g>
   </g>
   <g id="text_18">
    <!-- $V_{(\mathrm{m/s})} = V_{(\mathrm{km/h})} \times \frac{1000}{3600}$ -->
    <g transform="translate(1446.624 177.552) scale(0.14 -0.14)">
     <defs>
      <path id="SimHei-28" d="M 2975 -200 
L 2700 -475 
Q 2075 125 1762 775 
Q 1450 1425 1450 2250 
Q 1450 3075 1762 3725 
Q 2075 4375 2700 5000 
L 2975 4725 
Q 2400 4175 2112 3587 
Q 1825 3000 1825 2250 
Q 1825 1500 2112 912 
Q 2400 325 2975 -200 
z
" transform="scale(0.015625)"/>
      <path id="STIXGeneral-Regular-6d" d="M 4960 0 
L 3565 0 
L 3565 96 
Q 3834 122 3907 208 
Q 3981 294 3981 576 
L 3981 1894 
Q 3981 2278 3869 2444 
Q 3757 2611 3488 2611 
Q 3264 2611 3107 2521 
Q 2950 2432 2803 2221 
L 2803 608 
Q 2803 301 2896 201 
Q 2989 102 3264 96 
L 3264 0 
L 1830 0 
L 1830 96 
Q 2112 115 2189 188 
Q 2266 262 2266 550 
L 2266 1901 
Q 2266 2611 1850 2611 
Q 1670 2611 1468 2531 
Q 1267 2451 1171 2336 
Q 1088 2240 1088 2227 
L 1088 448 
Q 1088 243 1177 176 
Q 1267 109 1523 96 
L 1523 0 
L 102 0 
L 102 96 
Q 371 102 460 195 
Q 550 288 550 563 
L 550 2150 
Q 550 2387 499 2480 
Q 448 2573 326 2573 
Q 230 2573 122 2547 
L 122 2656 
Q 557 2778 1011 2944 
L 1062 2925 
L 1062 2451 
L 1075 2451 
Q 1363 2752 1584 2848 
Q 1805 2944 2054 2944 
Q 2560 2944 2733 2406 
Q 3232 2944 3776 2944 
Q 4518 2944 4518 1792 
L 4518 493 
Q 4518 282 4582 202 
Q 4646 122 4794 109 
L 4960 96 
L 4960 0 
z
" transform="scale(0.015625)"/>
      <path id="STIXGeneral-Regular-2f" d="M 1837 4326 
L 378 -90 
L -58 -90 
L 1408 4326 
L 1837 4326 
z
" transform="scale(0.015625)"/>
      <path id="STIXGeneral-Regular-73" d="M 998 1926 
L 1664 1523 
Q 1984 1331 2105 1164 
Q 2227 998 2227 736 
Q 2227 416 1961 176 
Q 1696 -64 1331 -64 
Q 1030 -64 864 -6 
Q 691 51 570 51 
Q 461 51 416 -26 
L 333 -26 
L 333 979 
L 435 979 
Q 538 512 730 294 
Q 922 77 1248 77 
Q 1485 77 1632 211 
Q 1779 346 1779 550 
Q 1779 845 1440 1030 
L 1094 1222 
Q 326 1651 326 2150 
Q 326 2522 566 2730 
Q 806 2938 1210 2938 
Q 1491 2938 1638 2867 
Q 1754 2816 1818 2816 
Q 1862 2816 1920 2880 
L 1990 2880 
L 2022 2010 
L 1926 2010 
Q 1818 2445 1654 2621 
Q 1491 2797 1203 2797 
Q 986 2797 854 2688 
Q 723 2579 723 2362 
Q 723 2253 796 2128 
Q 870 2003 998 1926 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-29" d="M 1675 2250 
Q 1675 1425 1362 775 
Q 1050 125 425 -475 
L 150 -200 
Q 725 325 1012 912 
Q 1300 1500 1300 2250 
Q 1300 3000 1012 3587 
Q 725 4175 150 4725 
L 425 5000 
Q 1050 4375 1362 3725 
Q 1675 3075 1675 2250 
z
" transform="scale(0.015625)"/>
      <path id="STIXGeneral-Regular-6b" d="M 3232 0 
L 1837 0 
L 1837 96 
L 1958 96 
Q 2093 96 2093 192 
Q 2093 250 2022 339 
L 1062 1606 
L 1062 429 
Q 1062 262 1142 185 
Q 1222 109 1414 102 
L 1542 96 
L 1542 0 
L 45 0 
L 45 96 
Q 390 154 457 214 
Q 525 275 525 525 
L 525 3610 
Q 525 3840 467 3920 
Q 410 4000 250 4000 
Q 211 4000 45 3987 
L 45 4090 
L 237 4141 
Q 621 4243 1037 4371 
L 1062 4358 
L 1062 1670 
L 1939 2451 
Q 2086 2586 2086 2675 
Q 2086 2739 2019 2761 
Q 1952 2784 1766 2790 
L 1766 2880 
L 3072 2880 
L 3072 2784 
Q 2752 2784 2505 2646 
Q 2259 2509 1690 1978 
L 1504 1805 
L 2483 563 
Q 2829 122 3232 96 
L 3232 0 
z
" transform="scale(0.015625)"/>
      <path id="STIXGeneral-Regular-68" d="M 3117 0 
L 1760 0 
L 1760 96 
Q 2042 128 2118 224 
Q 2195 320 2195 653 
L 2195 1920 
Q 2195 2598 1722 2598 
Q 1530 2598 1370 2508 
Q 1210 2419 1005 2195 
L 1005 653 
Q 1005 320 1081 224 
Q 1158 128 1440 96 
L 1440 0 
L 58 0 
L 58 96 
Q 333 134 400 224 
Q 467 314 467 653 
L 467 3667 
Q 467 3872 406 3933 
Q 346 3994 141 3994 
Q 83 3994 64 3987 
L 64 4090 
L 237 4141 
Q 730 4288 973 4371 
L 1005 4352 
L 1005 2406 
Q 1229 2694 1446 2819 
Q 1664 2944 1946 2944 
Q 2733 2944 2733 1926 
L 2733 653 
Q 2733 320 2793 233 
Q 2854 147 3117 96 
L 3117 0 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-56" transform="translate(0 0.757812)"/>
     <use xlink:href="#SimHei-28" transform="translate(50.799805 -12.953125) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-6d" transform="translate(85.799805 -12.953125) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-2f" transform="translate(140.259796 -12.953125) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-73" transform="translate(159.719788 -12.953125) scale(0.7)"/>
     <use xlink:href="#SimHei-29" transform="translate(186.949783 -12.953125) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(238.674936 0.757812)"/>
     <use xlink:href="#SimHei-56" transform="translate(303.114932 0.757812)"/>
     <use xlink:href="#SimHei-28" transform="translate(353.914737 -12.953125) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-6b" transform="translate(388.914737 -12.953125) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-6d" transform="translate(423.914726 -12.953125) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-2f" transform="translate(478.374718 -12.953125) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-68" transform="translate(497.834709 -12.953125) scale(0.7)"/>
     <use xlink:href="#SimHei-29" transform="translate(532.834698 -12.953125) scale(0.7)"/>
     <use xlink:href="#SimHei-d7" transform="translate(584.559851 0.757812)"/>
     <use xlink:href="#SimHei-31" transform="translate(698.999847 50.054688) scale(0.7)"/>
     <use xlink:href="#SimHei-30" transform="translate(733.999847 50.054688) scale(0.7)"/>
     <use xlink:href="#SimHei-30" transform="translate(768.999847 50.054688) scale(0.7)"/>
     <use xlink:href="#SimHei-30" transform="translate(803.999847 50.054688) scale(0.7)"/>
     <use xlink:href="#SimHei-33" transform="translate(698.999847 -30.6875) scale(0.7)"/>
     <use xlink:href="#SimHei-36" transform="translate(733.999847 -30.6875) scale(0.7)"/>
     <use xlink:href="#SimHei-30" transform="translate(768.999847 -30.6875) scale(0.7)"/>
     <use xlink:href="#SimHei-30" transform="translate(803.999847 -30.6875) scale(0.7)"/>
     <path d="M 698.999847 24.78125 
L 698.999847 31.03125 
L 838.999847 31.03125 
L 838.999847 24.78125 
L 698.999847 24.78125 
z
"/>
    </g>
   </g>
   <g id="text_19">
    <!-- 反应距离： -->
    <g transform="translate(1446.624 210) scale(0.18 -0.18)">
     <use xlink:href="#SimHei-53cd"/>
     <use xlink:href="#SimHei-5e94" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-8ddd" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-79bb" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-ff1a" transform="translate(400 0)"/>
    </g>
   </g>
   <g id="text_20">
    <!-- $D_{\mathrm{反应}} = V_0 \times t_{\mathrm{反应}}$ -->
    <g transform="translate(1446.624 234.336) scale(0.14 -0.14)">
     <use xlink:href="#SimHei-44" transform="translate(0 0.25)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(50.799805 -13.460938) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(85.799794 -13.460938) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(137.524936 0.25)"/>
     <use xlink:href="#SimHei-56" transform="translate(201.964932 0.25)"/>
     <use xlink:href="#SimHei-30" transform="translate(252.764737 -13.460938) scale(0.7)"/>
     <use xlink:href="#SimHei-d7" transform="translate(304.48989 0.25)"/>
     <use xlink:href="#SimHei-74" transform="translate(418.929886 0.25)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(469.729691 -13.460938) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(504.72968 -13.460938) scale(0.7)"/>
    </g>
   </g>
   <g id="text_21">
    <!-- 制动距离： -->
    <g transform="translate(1446.624 266.784) scale(0.18 -0.18)">
     <use xlink:href="#SimHei-5236"/>
     <use xlink:href="#SimHei-52a8" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-8ddd" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-79bb" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-ff1a" transform="translate(400 0)"/>
    </g>
   </g>
   <g id="text_22">
    <!-- $D_{\mathrm{制动}} = \frac{V_0^2}{2a}$ -->
    <g transform="translate(1446.624 291.12) scale(0.14 -0.14)">
     <use xlink:href="#SimHei-44" transform="translate(0 0.320312)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(50.799805 -13.390625) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(85.799794 -13.390625) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(137.524936 0.320312)"/>
     <use xlink:href="#SimHei-56" transform="translate(205.964932 65.53125) scale(0.7)"/>
     <use xlink:href="#SimHei-32" transform="translate(241.524796 92.546875) scale(0.49)"/>
     <use xlink:href="#SimHei-30" transform="translate(241.524796 49.535156) scale(0.49)"/>
     <use xlink:href="#SimHei-32" transform="translate(201.964932 -31.125) scale(0.7)"/>
     <use xlink:href="#SimHei-61" transform="translate(236.964932 -31.125) scale(0.7)"/>
     <path d="M 201.964932 24.34375 
L 201.964932 30.59375 
L 271.964932 30.59375 
L 271.964932 24.34375 
L 201.964932 24.34375 
z
"/>
    </g>
   </g>
   <g id="text_23">
    <!-- 步骤四：计算总制动距离 -->
    <g transform="translate(599.904 323.568) scale(0.18 -0.18)">
     <defs>
      <path id="SimHei-56db" d="M 5750 4750 
Q 5725 4125 5725 3425 
L 5725 950 
Q 5725 300 5750 -450 
L 5225 -450 
L 5225 25 
L 1150 25 
L 1150 -500 
L 625 -500 
Q 650 300 650 1000 
L 650 3425 
Q 650 4050 625 4750 
L 5750 4750 
z
M 5225 450 
L 5225 4350 
L 3975 4350 
L 3975 2225 
Q 3975 1950 4225 1950 
Q 4475 1950 5025 1975 
Q 4900 1700 4875 1500 
Q 4225 1500 3887 1512 
Q 3550 1525 3500 1975 
L 3500 4350 
L 2850 4350 
Q 2850 3050 2562 2412 
Q 2275 1775 1700 1200 
Q 1575 1375 1275 1525 
Q 1650 1800 1900 2175 
Q 2150 2550 2262 3000 
Q 2375 3450 2375 4350 
L 1150 4350 
L 1150 450 
L 5225 450 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-603b" d="M 4550 4850 
Q 4375 4650 4025 3900 
L 5100 3900 
Q 5075 3425 5075 2950 
Q 5075 2500 5100 2025 
L 1175 2025 
Q 1200 2550 1200 3000 
Q 1200 3475 1175 3900 
L 2175 3900 
Q 1900 4350 1525 4750 
Q 1700 4850 1875 5050 
Q 2350 4550 2575 4200 
Q 2400 4075 2250 3900 
L 3550 3900 
Q 3875 4700 3950 5100 
Q 4300 4925 4550 4850 
z
M 4600 2450 
L 4600 3525 
L 1650 3525 
L 1650 2450 
L 4600 2450 
z
M 2275 1450 
Q 2250 1050 2250 675 
Q 2250 300 2262 112 
Q 2275 -75 2675 -75 
L 4000 -75 
Q 4325 -75 4400 550 
Q 4650 400 4925 325 
Q 4775 -200 4562 -337 
Q 4350 -475 4000 -450 
L 2325 -450 
Q 1850 -450 1825 -175 
Q 1800 100 1800 525 
Q 1800 975 1775 1450 
L 2275 1450 
z
M 1350 1325 
Q 1050 300 825 -250 
Q 625 -150 375 -50 
Q 675 525 900 1500 
Q 1100 1425 1350 1325 
z
M 4950 1575 
Q 5700 825 6000 450 
Q 5825 275 5625 100 
Q 5375 450 4600 1275 
Q 4750 1375 4950 1575 
z
M 3350 575 
Q 2975 1175 2675 1500 
Q 2825 1650 3000 1800 
Q 3225 1575 3750 875 
Q 3525 775 3350 575 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-6b65"/>
     <use xlink:href="#SimHei-9aa4" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-56db" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-ff1a" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-8ba1" transform="translate(400 0)"/>
     <use xlink:href="#SimHei-7b97" transform="translate(500 0)"/>
     <use xlink:href="#SimHei-603b" transform="translate(600 0)"/>
     <use xlink:href="#SimHei-5236" transform="translate(700 0)"/>
     <use xlink:href="#SimHei-52a8" transform="translate(800 0)"/>
     <use xlink:href="#SimHei-8ddd" transform="translate(900 0)"/>
     <use xlink:href="#SimHei-79bb" transform="translate(1000 0)"/>
    </g>
   </g>
   <g id="text_24">
    <!-- $D_{\mathrm{总}} = D_{\mathrm{反应}} + D_{\mathrm{制动}} = 6.945 + 9.84 = 16.785$ m -->
    <g style="fill: #d32f2f" transform="translate(628.128 347.904) scale(0.16 -0.16)">
     <defs>
      <path id="SimHei-2b" d="M 3000 2125 
L 1775 2125 
L 1775 900 
L 1350 900 
L 1350 2125 
L 125 2125 
L 125 2525 
L 1350 2525 
L 1350 3750 
L 1775 3750 
L 1775 2525 
L 3000 2525 
L 3000 2125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-37" d="M 850 150 
Q 1300 2050 2425 3925 
L 275 3925 
L 275 4375 
L 2950 4375 
L 2950 3950 
Q 1775 2050 1400 150 
L 850 150 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-44" transform="translate(0 0.078125)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(50.799805 -13.632812) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(102.524947 0.078125)"/>
     <use xlink:href="#SimHei-44" transform="translate(166.964943 0.078125)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(217.764748 -13.632812) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(252.764737 -13.632812) scale(0.7)"/>
     <use xlink:href="#SimHei-2b" transform="translate(304.489879 0.078125)"/>
     <use xlink:href="#SimHei-44" transform="translate(368.929875 0.078125)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(419.72968 -13.632812) scale(0.7)"/>
     <use xlink:href="#STIXGeneral-Regular-a4" transform="translate(454.729669 -13.632812) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(506.454811 0.078125)"/>
     <use xlink:href="#SimHei-36" transform="translate(570.894807 0.078125)"/>
     <use xlink:href="#SimHei-2e" transform="translate(620.894807 0.078125)"/>
     <use xlink:href="#SimHei-39" transform="translate(670.894807 0.078125)"/>
     <use xlink:href="#SimHei-34" transform="translate(720.894807 0.078125)"/>
     <use xlink:href="#SimHei-35" transform="translate(770.894807 0.078125)"/>
     <use xlink:href="#SimHei-2b" transform="translate(835.334804 0.078125)"/>
     <use xlink:href="#SimHei-39" transform="translate(899.7748 0.078125)"/>
     <use xlink:href="#SimHei-2e" transform="translate(949.7748 0.078125)"/>
     <use xlink:href="#SimHei-38" transform="translate(999.7748 0.078125)"/>
     <use xlink:href="#SimHei-34" transform="translate(1049.7748 0.078125)"/>
     <use xlink:href="#SimHei-3d" transform="translate(1114.214796 0.078125)"/>
     <use xlink:href="#SimHei-31" transform="translate(1178.654793 0.078125)"/>
     <use xlink:href="#SimHei-36" transform="translate(1228.654793 0.078125)"/>
     <use xlink:href="#SimHei-2e" transform="translate(1278.654793 0.078125)"/>
     <use xlink:href="#SimHei-37" transform="translate(1328.654793 0.078125)"/>
     <use xlink:href="#SimHei-38" transform="translate(1378.654793 0.078125)"/>
     <use xlink:href="#SimHei-35" transform="translate(1428.654793 0.078125)"/>
     <use xlink:href="#SimHei-20" transform="translate(1478.654793 0.078125)"/>
     <use xlink:href="#SimHei-6d" transform="translate(1528.654793 0.078125)"/>
    </g>
   </g>
   <g id="text_25">
    <!-- 结论：$16.785$ m $&gt; 15.0$ m，测试不达标！ -->
    <g style="fill: #d32f2f" transform="translate(776.86 420.912) scale(0.2 -0.2)">
     <defs>
      <path id="SimHei-7ed3" d="M 5725 1925 
Q 5700 1600 5700 1200 
L 5700 350 
Q 5700 -125 5725 -475 
L 5225 -475 
L 5225 0 
L 3525 0 
L 3525 -525 
L 3025 -525 
Q 3050 125 3050 325 
L 3050 1250 
Q 3050 1550 3025 1925 
L 5725 1925 
z
M 1900 4850 
Q 1700 4600 1475 4200 
Q 1250 3800 925 3200 
Q 1475 3225 1725 3225 
Q 1950 3625 2100 4000 
Q 2350 3825 2650 3675 
Q 2525 3575 2212 3125 
Q 1900 2675 1225 1650 
Q 2050 1775 2525 1850 
Q 2450 1575 2450 1375 
Q 2225 1350 1650 1275 
Q 1075 1200 525 1050 
Q 425 1300 350 1550 
Q 650 1625 950 2012 
Q 1250 2400 1475 2825 
Q 1300 2825 962 2787 
Q 625 2750 375 2675 
Q 300 2925 225 3150 
Q 500 3375 812 3925 
Q 1125 4475 1300 5125 
Q 1600 4975 1900 4850 
z
M 4075 4175 
Q 4075 4600 4050 5175 
L 4575 5175 
Q 4550 4725 4550 4175 
L 5275 4175 
Q 5750 4175 6150 4200 
L 6150 3750 
Q 5750 3775 5300 3775 
L 4550 3775 
L 4550 2950 
L 5125 2950 
Q 5475 2950 5825 2975 
L 5825 2525 
Q 5525 2550 5100 2550 
L 3650 2550 
Q 3225 2550 2900 2525 
L 2900 2975 
Q 3225 2950 3600 2950 
L 4075 2950 
L 4075 3775 
L 3525 3775 
Q 3075 3775 2675 3750 
L 2675 4200 
Q 3000 4175 3475 4175 
L 4075 4175 
z
M 5225 400 
L 5225 1525 
L 3525 1525 
L 3525 400 
L 5225 400 
z
M 2575 650 
Q 2500 425 2525 175 
Q 2050 100 1450 0 
Q 850 -100 425 -225 
Q 325 100 225 350 
Q 750 375 1175 437 
Q 1600 500 2575 650 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8bba" d="M 3500 1600 
Q 3825 1725 4250 2000 
Q 4675 2275 4975 2575 
Q 5225 2275 5400 2100 
Q 5050 1950 4550 1675 
Q 4050 1400 3500 1150 
L 3500 300 
Q 3500 -100 3800 -100 
L 4825 -100 
Q 5125 -100 5250 137 
Q 5375 375 5375 575 
Q 5675 350 5925 275 
Q 5750 -225 5550 -375 
Q 5350 -525 5100 -525 
L 3550 -525 
Q 3025 -475 3025 0 
L 3025 2175 
Q 3025 2525 3000 2850 
L 3525 2850 
Q 3500 2400 3500 2200 
L 3500 1600 
z
M 4175 4850 
Q 4400 4350 4950 3687 
Q 5500 3025 6175 2700 
Q 5975 2575 5800 2275 
Q 5100 2750 4637 3375 
Q 4175 4000 3950 4425 
Q 3725 3950 3387 3425 
Q 3050 2900 2325 2200 
Q 2175 2375 1950 2575 
Q 2350 2825 2650 3175 
Q 2950 3525 3200 3962 
Q 3450 4400 3575 4725 
Q 3700 5050 3750 5250 
Q 3975 5150 4275 5050 
L 4175 4850 
z
M 1600 3150 
Q 1575 2750 1575 2275 
L 1575 550 
Q 1950 825 2400 1300 
Q 2500 1075 2650 800 
Q 2250 475 1900 187 
Q 1550 -100 1250 -375 
Q 1150 -225 900 50 
Q 1100 250 1100 450 
L 1100 2700 
L 800 2700 
Q 550 2700 275 2675 
L 275 3175 
Q 550 3150 800 3150 
L 1600 3150 
z
M 975 5000 
Q 1550 4525 2125 4025 
Q 1950 3875 1750 3650 
Q 1325 4150 650 4650 
Q 825 4800 975 5000 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-3e" d="M 2950 2250 
L 425 -25 
L 200 250 
L 2450 2250 
L 200 4300 
L 450 4550 
L 2950 2250 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-ff0c" d="M 875 950 
L 1775 950 
L 1775 -50 
Q 1700 -675 1150 -975 
L 900 -750 
Q 1350 -500 1350 0 
L 875 0 
L 875 950 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-4e0d" d="M 5975 4450 
Q 5525 4475 5200 4475 
L 3725 4475 
Q 3550 4125 3300 3700 
L 3300 575 
Q 3300 75 3325 -550 
L 2775 -550 
Q 2800 50 2800 575 
L 2800 3025 
Q 2025 1950 700 1050 
Q 500 1350 325 1475 
Q 825 1700 1650 2437 
Q 2475 3175 3175 4475 
L 1525 4475 
Q 1050 4475 525 4450 
L 525 4950 
Q 1275 4925 1675 4925 
L 5175 4925 
Q 5500 4925 5975 4950 
L 5975 4450 
z
M 5450 1125 
Q 4975 1725 3750 2975 
Q 3950 3150 4075 3325 
Q 4675 2775 5100 2362 
Q 5525 1950 5900 1550 
Q 5650 1350 5450 1125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8fbe" d="M 1700 2975 
Q 1675 2500 1675 2225 
L 1675 775 
Q 2025 350 2600 150 
Q 3175 -50 4375 -50 
Q 5600 -50 6150 75 
Q 5975 -325 5975 -550 
Q 4925 -575 4212 -550 
Q 3500 -525 3025 -437 
Q 2550 -350 2287 -225 
Q 2025 -100 1737 137 
Q 1450 375 1212 125 
Q 975 -125 700 -475 
Q 525 -300 275 -100 
Q 625 200 1175 700 
L 1175 2550 
Q 825 2550 375 2525 
L 375 3000 
Q 900 2975 1700 2975 
z
M 3700 3700 
Q 3700 4800 3675 5225 
Q 4100 5175 4275 5150 
Q 4200 4825 4175 3700 
Q 5475 3700 5875 3725 
L 5875 3225 
Q 5475 3250 4150 3250 
Q 4075 2400 3687 1700 
Q 3300 1000 2500 375 
Q 2400 575 2125 775 
Q 2775 1225 3162 1812 
Q 3550 2400 3650 3250 
Q 2625 3250 2150 3225 
L 2150 3725 
Q 2625 3700 3700 3700 
z
M 4475 2450 
Q 5600 1225 5925 800 
Q 5675 650 5450 425 
Q 5075 1000 4100 2100 
Q 4275 2225 4475 2450 
z
M 1100 5125 
Q 1575 4575 1950 4075 
Q 1725 3900 1525 3700 
Q 1125 4325 700 4800 
Q 900 4925 1100 5125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6807" d="M 1275 4400 
Q 1275 4800 1250 5175 
L 1750 5175 
Q 1725 4825 1725 4400 
L 1725 3800 
Q 2125 3800 2575 3825 
L 2575 3350 
Q 2125 3375 1725 3375 
L 1725 2525 
Q 1900 2650 2075 2800 
Q 2400 2275 2650 1775 
Q 2375 1650 2175 1500 
Q 2025 2000 1725 2425 
L 1725 475 
Q 1725 -50 1750 -575 
L 1250 -575 
Q 1275 -25 1275 475 
L 1275 2100 
Q 1025 1400 500 750 
Q 350 950 125 1125 
Q 550 1550 812 2187 
Q 1075 2825 1150 3375 
Q 675 3375 325 3325 
L 325 3825 
Q 800 3800 1275 3800 
L 1275 4400 
z
M 5175 3125 
Q 5675 3125 6050 3150 
L 6050 2675 
Q 5675 2700 5275 2700 
L 4500 2700 
L 4500 175 
Q 4500 -225 4275 -350 
Q 4050 -475 3475 -550 
Q 3425 -250 3275 25 
Q 3775 25 3900 62 
Q 4025 100 4025 350 
L 4025 2700 
L 3400 2700 
Q 2775 2700 2500 2650 
L 2500 3150 
Q 2825 3125 3400 3125 
L 5175 3125 
z
M 4900 4775 
Q 5425 4775 5750 4800 
L 5750 4300 
Q 5275 4350 4750 4350 
L 3525 4350 
Q 3125 4350 2750 4300 
L 2750 4800 
Q 3100 4775 3525 4775 
L 4900 4775 
z
M 5275 2025 
Q 5825 1175 6200 500 
Q 5925 375 5650 200 
Q 5375 925 4825 1725 
Q 5075 1850 5275 2025 
z
M 3600 1775 
Q 3500 1625 3225 975 
Q 2950 325 2800 50 
Q 2575 175 2300 275 
Q 2625 775 2787 1187 
Q 2950 1600 3050 2000 
Q 3325 1875 3600 1775 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-ff01" d="M 1575 4550 
L 2325 4550 
L 2150 1550 
L 1750 1550 
L 1575 4550 
z
M 1575 750 
L 2300 750 
L 2300 0 
L 1575 0 
L 1575 750 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-7ed3" transform="translate(0 0.96875)"/>
     <use xlink:href="#SimHei-8bba" transform="translate(100 0.96875)"/>
     <use xlink:href="#SimHei-ff1a" transform="translate(200 0.96875)"/>
     <use xlink:href="#SimHei-31" transform="translate(300 0.96875)"/>
     <use xlink:href="#SimHei-36" transform="translate(350 0.96875)"/>
     <use xlink:href="#SimHei-2e" transform="translate(400 0.96875)"/>
     <use xlink:href="#SimHei-37" transform="translate(450 0.96875)"/>
     <use xlink:href="#SimHei-38" transform="translate(500 0.96875)"/>
     <use xlink:href="#SimHei-35" transform="translate(550 0.96875)"/>
     <use xlink:href="#SimHei-20" transform="translate(600 0.96875)"/>
     <use xlink:href="#SimHei-6d" transform="translate(650 0.96875)"/>
     <use xlink:href="#SimHei-20" transform="translate(700 0.96875)"/>
     <use xlink:href="#SimHei-3e" transform="translate(764.439996 0.96875)"/>
     <use xlink:href="#SimHei-31" transform="translate(828.879993 0.96875)"/>
     <use xlink:href="#SimHei-35" transform="translate(878.879993 0.96875)"/>
     <use xlink:href="#SimHei-2e" transform="translate(928.879993 0.96875)"/>
     <use xlink:href="#SimHei-30" transform="translate(978.879993 0.96875)"/>
     <use xlink:href="#SimHei-20" transform="translate(1028.879993 0.96875)"/>
     <use xlink:href="#SimHei-6d" transform="translate(1078.879993 0.96875)"/>
     <use xlink:href="#SimHei-ff0c" transform="translate(1128.879993 0.96875)"/>
     <use xlink:href="#SimHei-6d4b" transform="translate(1228.879993 0.96875)"/>
     <use xlink:href="#SimHei-8bd5" transform="translate(1328.879993 0.96875)"/>
     <use xlink:href="#SimHei-4e0d" transform="translate(1428.879993 0.96875)"/>
     <use xlink:href="#SimHei-8fbe" transform="translate(1528.879993 0.96875)"/>
     <use xlink:href="#SimHei-6807" transform="translate(1628.879993 0.96875)"/>
     <use xlink:href="#SimHei-ff01" transform="translate(1728.879993 0.96875)"/>
    </g>
   </g>
   <g id="text_26">
    <!-- 制动过程示意图 -->
    <g transform="translate(769.92 510.144) scale(0.24 -0.24)">
     <defs>
      <path id="SimHei-8fc7" d="M 250 -200 
Q 1025 475 1275 650 
L 1275 2475 
Q 675 2475 400 2450 
L 400 2950 
Q 625 2900 1325 2900 
L 1800 2900 
Q 1775 2525 1775 2150 
L 1775 600 
Q 2500 -25 3912 -37 
Q 5325 -50 6150 75 
Q 6000 -175 5925 -550 
L 4425 -550 
Q 3800 -550 3275 -512 
Q 2750 -475 2387 -300 
Q 2025 -125 1750 75 
Q 1475 275 1237 37 
Q 1000 -200 700 -625 
Q 550 -375 250 -200 
z
M 3625 850 
Q 4200 825 4350 875 
Q 4500 925 4500 1300 
L 4500 3575 
L 2875 3575 
Q 2600 3575 2275 3550 
L 2275 4025 
Q 2600 4000 3075 4000 
L 4500 4000 
Q 4500 4650 4450 5125 
L 5025 5125 
Q 5000 4800 5000 4000 
Q 5525 4000 5950 4025 
L 5950 3550 
Q 5550 3575 5000 3575 
L 5000 1100 
Q 5000 525 4662 412 
Q 4325 300 3825 275 
Q 3775 550 3625 850 
z
M 3125 1600 
Q 2900 2275 2575 2850 
Q 2800 2950 3050 3125 
Q 3500 2225 3625 1875 
Q 3450 1775 3125 1600 
z
M 1150 4975 
Q 1400 4675 1925 3875 
Q 1675 3725 1450 3550 
Q 950 4400 700 4625 
Q 925 4750 1150 4975 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7a0b" d="M 5825 4975 
Q 5800 4425 5800 3975 
Q 5800 3550 5825 3125 
L 3025 3125 
Q 3050 3600 3050 4025 
Q 3050 4450 3025 4975 
L 5825 4975 
z
M 400 4825 
Q 825 4875 1362 4987 
Q 1900 5100 2275 5275 
Q 2450 4950 2625 4725 
Q 2450 4750 2262 4712 
Q 2075 4675 1850 4650 
L 1850 3425 
Q 2200 3425 2600 3450 
L 2600 2975 
Q 2200 3000 1850 3000 
L 1850 800 
Q 1850 250 1875 -675 
L 1375 -675 
Q 1400 250 1400 775 
L 1400 1925 
Q 1050 1225 625 600 
Q 450 850 225 1050 
Q 575 1400 850 1850 
Q 1125 2300 1350 3000 
Q 725 3000 425 2975 
L 425 3450 
Q 850 3425 1050 3425 
L 1400 3425 
L 1400 4550 
Q 1075 4475 575 4400 
Q 500 4625 400 4825 
z
M 5100 2500 
Q 5500 2500 5950 2525 
L 5950 2075 
Q 5500 2100 5100 2100 
L 4625 2100 
L 4625 1300 
L 4900 1300 
Q 5275 1300 5775 1325 
L 5775 850 
Q 5275 875 4975 875 
L 4625 875 
L 4625 0 
L 5225 0 
Q 5675 0 6175 25 
L 6175 -450 
Q 5775 -425 5225 -425 
L 3550 -425 
Q 3000 -425 2600 -475 
L 2600 25 
Q 3000 0 3550 0 
L 4150 0 
L 4150 875 
L 3800 875 
Q 3400 875 3050 825 
L 3050 1325 
Q 3400 1300 3800 1300 
L 4150 1300 
L 4150 2100 
L 3750 2100 
Q 3300 2100 2900 2075 
L 2900 2525 
Q 3300 2500 3750 2500 
L 5100 2500 
z
M 5275 3575 
L 5275 4550 
L 3500 4550 
L 3500 3575 
L 5275 3575 
z
M 2300 2400 
Q 2550 2025 2825 1525 
Q 2675 1425 2425 1225 
Q 2175 1775 1925 2125 
Q 2075 2250 2300 2400 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-5236"/>
     <use xlink:href="#SimHei-52a8" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-8fc7" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-7a0b" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-793a" transform="translate(400 0)"/>
     <use xlink:href="#SimHei-610f" transform="translate(500 0)"/>
     <use xlink:href="#SimHei-56fe" transform="translate(600 0)"/>
    </g>
   </g>
   <g id="text_27">
    <!-- 车辆 -->
    <g style="fill: #ffffff" transform="translate(216.992 591.264) scale(0.16 -0.16)">
     <defs>
      <path id="SimHei-8f86" d="M 3000 800 
Q 3175 1100 3312 1675 
Q 3450 2250 3475 3050 
L 2950 3050 
L 2950 -525 
L 2500 -525 
Q 2525 -25 2525 350 
L 2525 2575 
Q 2525 3050 2500 3450 
L 3475 3450 
L 3475 4425 
Q 2925 4425 2500 4400 
L 2500 4850 
Q 2900 4825 3375 4825 
L 5425 4825 
L 6025 4875 
L 6025 4400 
Q 5700 4425 4900 4425 
L 4900 3450 
L 5975 3450 
Q 5950 3100 5950 2375 
L 5950 0 
Q 5925 -325 5675 -400 
Q 5425 -475 5025 -550 
Q 4925 -200 4825 25 
Q 5325 -25 5412 12 
Q 5500 50 5550 250 
L 5550 3050 
L 4900 3050 
Q 4875 2600 4850 2300 
Q 5325 1350 5475 1025 
Q 5250 900 5075 750 
Q 5000 1050 4750 1775 
Q 4475 750 4300 275 
Q 4075 450 3850 575 
Q 4075 875 4262 1462 
Q 4450 2050 4500 3050 
L 3875 3050 
L 3825 2450 
Q 4000 2250 4300 1725 
Q 4150 1625 4000 1450 
Q 3875 1725 3775 1975 
Q 3575 950 3400 500 
Q 3225 675 3000 800 
z
M 1775 800 
Q 1775 -125 1800 -600 
L 1350 -600 
Q 1375 50 1375 700 
Q 800 575 500 425 
Q 425 700 325 950 
Q 575 975 1375 1125 
L 1375 2100 
Q 1100 2100 850 2075 
Q 600 2050 400 2000 
L 225 2450 
Q 450 2625 575 3037 
Q 700 3450 825 3825 
Q 650 3825 300 3800 
L 300 4250 
Q 625 4225 925 4225 
Q 1050 4800 1100 5275 
Q 1225 5225 1650 5125 
Q 1550 4800 1375 4225 
Q 1925 4225 2350 4250 
L 2350 3775 
Q 1925 3825 1275 3825 
Q 1175 3450 850 2500 
L 1375 2500 
Q 1375 3100 1350 3450 
L 1800 3450 
Q 1775 3150 1775 2500 
Q 2150 2500 2400 2525 
L 2400 2075 
Q 2125 2100 1775 2100 
L 1775 1200 
Q 2200 1300 2450 1350 
Q 2350 1125 2400 950 
Q 2075 900 1775 800 
z
M 4475 3450 
L 4475 4425 
L 3900 4425 
L 3900 3450 
L 4475 3450 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-8f66"/>
     <use xlink:href="#SimHei-8f86" transform="translate(100 0)"/>
    </g>
   </g>
   <g id="text_28">
    <!-- 障碍物 -->
    <g style="fill: #ffffff" transform="translate(1366.176 587.208) scale(0.16 -0.16)">
     <defs>
      <path id="SimHei-969c" d="M 5650 2800 
Q 5625 2425 5625 2200 
L 5625 1675 
Q 5625 1350 5650 1025 
L 4400 1025 
L 4400 550 
L 5325 550 
Q 5725 550 6175 575 
L 6175 175 
Q 5800 200 5325 200 
L 4400 200 
Q 4400 -300 4425 -625 
L 3900 -625 
Q 3925 -300 3925 200 
L 3225 200 
Q 2675 200 2175 175 
L 2175 575 
Q 2650 550 3225 550 
L 3925 550 
L 3925 1025 
L 2700 1025 
Q 2725 1275 2725 1700 
L 2725 2200 
Q 2725 2525 2700 2800 
L 5650 2800 
z
M 2350 4750 
Q 2225 4550 2037 3987 
Q 1850 3425 1675 2950 
Q 2150 2350 2250 2037 
Q 2350 1725 2337 1425 
Q 2325 1125 2025 962 
Q 1725 800 1325 725 
Q 1300 975 1150 1250 
Q 1650 1225 1787 1362 
Q 1925 1500 1850 1775 
Q 1775 2050 1600 2300 
Q 1425 2550 1175 2875 
Q 1300 3200 1450 3600 
Q 1600 4000 1675 4400 
L 900 4400 
L 900 -550 
L 400 -550 
Q 425 0 425 850 
L 425 3725 
Q 425 4275 400 4750 
L 2350 4750 
z
M 4500 3575 
Q 4575 3725 4600 3887 
Q 4625 4050 4650 4200 
L 3625 4200 
Q 3075 4200 2650 4175 
L 2650 4575 
Q 3075 4550 3600 4550 
L 4925 4550 
Q 5425 4550 5875 4575 
L 5875 4175 
Q 5425 4200 4925 4200 
L 4700 4200 
Q 4875 4125 5175 4025 
Q 5050 3800 4950 3575 
L 5325 3575 
Q 5775 3575 6175 3600 
L 6175 3200 
Q 5775 3225 5325 3225 
L 3225 3225 
Q 2775 3225 2325 3200 
L 2325 3600 
Q 2600 3575 2975 3575 
L 3275 3575 
Q 3225 3800 3150 4000 
Q 3350 4025 3600 4100 
Q 3675 3825 3800 3575 
L 4500 3575 
z
M 5150 1375 
L 5150 1725 
L 3200 1725 
L 3200 1375 
L 5150 1375 
z
M 5150 2100 
L 5150 2450 
L 3200 2450 
L 3200 2100 
L 5150 2100 
z
M 3975 4600 
Q 3900 4875 3750 5150 
L 4250 5300 
Q 4350 5050 4475 4750 
Q 4200 4675 3975 4600 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-788d" d="M 1775 4675 
Q 2350 4675 2575 4700 
L 2575 4300 
Q 2275 4325 1625 4325 
Q 1500 3725 1225 3050 
L 2425 3050 
Q 2400 2625 2400 1700 
Q 2400 775 2425 25 
L 2000 25 
L 2000 525 
L 1275 525 
L 1275 -225 
L 850 -225 
Q 875 225 875 850 
L 875 2350 
Q 700 2100 500 1850 
Q 375 2100 200 2225 
Q 575 2600 825 3175 
Q 1075 3750 1175 4325 
Q 675 4325 425 4300 
L 425 4700 
Q 675 4675 1075 4675 
L 1775 4675 
z
M 5675 4850 
Q 5650 4275 5650 3825 
Q 5650 3375 5675 2800 
L 2875 2800 
Q 2900 3525 2900 3900 
Q 2900 4275 2875 4850 
L 5675 4850 
z
M 5000 2400 
Q 5550 2400 5925 2425 
L 5925 2025 
Q 5625 2050 5225 2050 
L 5225 1500 
Q 5775 1500 6175 1525 
L 6175 1125 
Q 5775 1150 5225 1150 
L 5225 100 
Q 5250 -275 5100 -425 
Q 4950 -575 4300 -650 
Q 4275 -450 4075 -150 
Q 4525 -175 4650 -100 
Q 4775 -25 4775 175 
L 4775 1150 
L 3525 1150 
Q 3200 1150 2575 1125 
L 2575 1525 
Q 3050 1500 3700 1500 
L 4775 1500 
L 4775 2050 
L 3475 2050 
Q 3200 2050 2750 2025 
L 2750 2425 
Q 3050 2400 3475 2400 
L 5000 2400 
z
M 2000 875 
L 2000 2700 
L 1275 2700 
L 1275 875 
L 2000 875 
z
M 5225 3175 
L 5225 3650 
L 3325 3650 
L 3325 3175 
L 5225 3175 
z
M 5225 4000 
L 5225 4475 
L 3325 4475 
L 3325 4000 
L 5225 4000 
z
M 3350 1125 
Q 3750 675 4025 400 
Q 3825 250 3675 75 
Q 3325 550 3050 800 
Q 3175 925 3350 1125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7269" d="M 5550 3775 
L 5225 3775 
Q 5175 2925 5062 2312 
Q 4950 1700 4700 1225 
Q 4450 750 4087 337 
Q 3725 -75 3100 -475 
Q 2950 -250 2650 -75 
Q 3275 175 3662 575 
Q 4050 975 4275 1462 
Q 4500 1950 4600 2537 
Q 4700 3125 4750 3775 
L 4325 3775 
Q 4250 3025 3912 2275 
Q 3575 1525 3075 1025 
Q 2925 1250 2600 1350 
Q 3175 1825 3462 2400 
Q 3750 2975 3875 3775 
L 3500 3775 
Q 3175 3125 2950 2800 
Q 2750 2975 2500 3050 
Q 3025 3750 3275 4375 
Q 3525 5000 3575 5275 
Q 3925 5125 4200 5050 
Q 4075 4950 3962 4725 
Q 3850 4500 3725 4200 
L 6075 4200 
Q 5975 3100 5937 1850 
Q 5900 600 5862 225 
Q 5825 -150 5500 -287 
Q 5175 -425 4550 -500 
Q 4525 -225 4300 75 
Q 5025 25 5187 100 
Q 5350 175 5375 525 
L 5550 3775 
z
M 1250 4325 
Q 1100 4125 1025 3700 
L 1600 3700 
L 1600 4525 
Q 1600 4900 1550 5200 
L 2100 5200 
Q 2075 4900 2075 4525 
L 2075 3700 
Q 2325 3700 2650 3725 
L 2650 3275 
Q 2300 3300 2075 3300 
L 2075 2000 
Q 2500 2100 2775 2225 
Q 2775 2000 2800 1775 
Q 2475 1675 2075 1550 
L 2075 375 
Q 2075 -50 2100 -600 
L 1550 -600 
Q 1600 -100 1600 375 
L 1600 1425 
Q 1325 1350 1062 1262 
Q 800 1175 500 1025 
Q 425 1250 250 1575 
Q 700 1625 1600 1850 
L 1600 3300 
L 950 3300 
Q 850 2825 725 2375 
Q 475 2500 225 2500 
Q 450 3025 537 3512 
Q 625 4000 650 4425 
Q 925 4350 1250 4325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-969c"/>
     <use xlink:href="#SimHei-788d" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-7269" transform="translate(200 0)"/>
    </g>
   </g>
   <g id="text_29">
    <!-- $V_0=50$ km/h -->
    <g transform="translate(196.802 538.536) scale(0.14 -0.14)">
     <use xlink:href="#SimHei-56" transform="translate(0 0.5625)"/>
     <use xlink:href="#SimHei-30" transform="translate(50.799805 -13.148438) scale(0.7)"/>
     <use xlink:href="#SimHei-3d" transform="translate(102.524957 0.5625)"/>
     <use xlink:href="#SimHei-35" transform="translate(166.964954 0.5625)"/>
     <use xlink:href="#SimHei-30" transform="translate(216.964954 0.5625)"/>
     <use xlink:href="#SimHei-20" transform="translate(266.964954 0.5625)"/>
     <use xlink:href="#SimHei-6b" transform="translate(316.964954 0.5625)"/>
     <use xlink:href="#SimHei-6d" transform="translate(366.964954 0.5625)"/>
     <use xlink:href="#SimHei-2f" transform="translate(416.964954 0.5625)"/>
     <use xlink:href="#SimHei-68" transform="translate(466.964954 0.5625)"/>
    </g>
   </g>
   <g id="text_30">
    <!-- $(13.89$ m/s$)$ -->
    <g transform="translate(199.992 550.704) scale(0.12 -0.12)">
     <use xlink:href="#SimHei-28" transform="translate(0 0.875)"/>
     <use xlink:href="#SimHei-31" transform="translate(50 0.875)"/>
     <use xlink:href="#SimHei-33" transform="translate(100 0.875)"/>
     <use xlink:href="#SimHei-2e" transform="translate(150 0.875)"/>
     <use xlink:href="#SimHei-38" transform="translate(200 0.875)"/>
     <use xlink:href="#SimHei-39" transform="translate(250 0.875)"/>
     <use xlink:href="#SimHei-20" transform="translate(300 0.875)"/>
     <use xlink:href="#SimHei-6d" transform="translate(350 0.875)"/>
     <use xlink:href="#SimHei-2f" transform="translate(400 0.875)"/>
     <use xlink:href="#SimHei-73" transform="translate(450 0.875)"/>
     <use xlink:href="#SimHei-29" transform="translate(500 0.875)"/>
    </g>
   </g>
   <g id="patch_14">
    <path d="M 295.911494 534.48 
Q 395.279844 534.48 494.648194 534.48 
" style="fill: none; stroke: #ff9800; stroke-width: 4; stroke-linecap: round"/>
    <path d="M 302.311494 537.68 
L 295.911494 534.48 
L 302.311494 531.28 
" style="fill: none; stroke: #ff9800; stroke-width: 4; stroke-linecap: round"/>
    <path d="M 488.248194 531.28 
L 494.648194 534.48 
L 488.248194 537.68 
" style="fill: none; stroke: #ff9800; stroke-width: 4; stroke-linecap: round"/>
   </g>
   <g id="text_31">
    <!-- 反应距离 -->
    <g style="fill: #4caf50" transform="translate(363.28 514.2) scale(0.16 -0.16)">
     <use xlink:href="#SimHei-53cd"/>
     <use xlink:href="#SimHei-5e94" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-8ddd" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-79bb" transform="translate(300 0)"/>
    </g>
   </g>
   <g id="text_32">
    <!-- 6.945 m -->
    <g style="fill: #4caf50" transform="translate(367.28 530.424) scale(0.16 -0.16)">
     <use xlink:href="#SimHei-36"/>
     <use xlink:href="#SimHei-2e" transform="translate(50 0)"/>
     <use xlink:href="#SimHei-39" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-34" transform="translate(150 0)"/>
     <use xlink:href="#SimHei-35" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-20" transform="translate(250 0)"/>
     <use xlink:href="#SimHei-6d" transform="translate(300 0)"/>
    </g>
   </g>
   <g id="patch_15">
    <path d="M 507.591494 534.48 
Q 712.798292 534.48 918.005089 534.48 
" style="fill: none; stroke: #ff9800; stroke-width: 4; stroke-linecap: round"/>
    <path d="M 513.991494 537.68 
L 507.591494 534.48 
L 513.991494 531.28 
" style="fill: none; stroke: #ff9800; stroke-width: 4; stroke-linecap: round"/>
    <path d="M 911.605089 531.28 
L 918.005089 534.48 
L 911.605089 537.68 
" style="fill: none; stroke: #ff9800; stroke-width: 4; stroke-linecap: round"/>
   </g>
   <g id="text_33">
    <!-- 制动距离 -->
    <g style="fill: #4caf50" transform="translate(680.8 514.2) scale(0.16 -0.16)">
     <use xlink:href="#SimHei-5236"/>
     <use xlink:href="#SimHei-52a8" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-8ddd" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-79bb" transform="translate(300 0)"/>
    </g>
   </g>
   <g id="text_34">
    <!-- 9.84 m -->
    <g style="fill: #4caf50" transform="translate(688.8 530.424) scale(0.16 -0.16)">
     <use xlink:href="#SimHei-39"/>
     <use xlink:href="#SimHei-2e" transform="translate(50 0)"/>
     <use xlink:href="#SimHei-38" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-34" transform="translate(150 0)"/>
     <use xlink:href="#SimHei-20" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-6d" transform="translate(250 0)"/>
    </g>
   </g>
   <g id="patch_16">
    <path d="M 297.031143 502.032 
Q 606.958323 502.032 916.885503 502.032 
" style="fill: none; stroke: #d32f2f; stroke-width: 5; stroke-linecap: round"/>
    <path d="M 303.431143 505.232 
L 297.031143 502.032 
L 303.431143 498.832 
" style="fill: none; stroke: #d32f2f; stroke-width: 5; stroke-linecap: round"/>
    <path d="M 910.485503 498.832 
L 916.885503 502.032 
L 910.485503 505.232 
" style="fill: none; stroke: #d32f2f; stroke-width: 5; stroke-linecap: round"/>
   </g>
   <g id="text_35">
    <!-- 总制动距离 16.785 m -->
    <g style="fill: #d32f2f" transform="translate(521.46 481.752) scale(0.18 -0.18)">
     <use xlink:href="#SimHei-603b"/>
     <use xlink:href="#SimHei-5236" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-52a8" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-8ddd" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-79bb" transform="translate(400 0)"/>
     <use xlink:href="#SimHei-20" transform="translate(500 0)"/>
     <use xlink:href="#SimHei-31" transform="translate(550 0)"/>
     <use xlink:href="#SimHei-36" transform="translate(600 0)"/>
     <use xlink:href="#SimHei-2e" transform="translate(650 0)"/>
     <use xlink:href="#SimHei-37" transform="translate(700 0)"/>
     <use xlink:href="#SimHei-38" transform="translate(750 0)"/>
     <use xlink:href="#SimHei-35" transform="translate(800 0)"/>
     <use xlink:href="#SimHei-20" transform="translate(850 0)"/>
     <use xlink:href="#SimHei-6d" transform="translate(900 0)"/>
    </g>
   </g>
   <g id="text_36">
    <!-- 场景初始距离 15.0 m -->
    <g style="fill: #4caf50" transform="translate(742.64 664.272) scale(0.16 -0.16)">
     <defs>
      <path id="SimHei-573a" d="M 5625 5000 
L 5625 4600 
Q 5275 4525 4862 4150 
Q 4450 3775 3750 3100 
L 6000 3100 
Q 5975 2925 5950 2300 
L 5850 425 
Q 5825 -200 5587 -350 
Q 5350 -500 4750 -550 
Q 4700 -250 4525 50 
Q 5050 0 5200 75 
Q 5350 150 5375 400 
L 5475 2675 
L 5000 2675 
Q 4875 1925 4700 1425 
Q 4525 925 4287 562 
Q 4050 200 3750 -62 
Q 3450 -325 3000 -600 
Q 2875 -350 2650 -175 
Q 3075 50 3425 350 
Q 3775 650 4000 1050 
Q 4225 1450 4337 1900 
Q 4450 2350 4500 2675 
L 4000 2675 
Q 3950 2300 3787 1912 
Q 3625 1525 3325 1150 
Q 3025 775 2550 425 
Q 2450 600 2175 800 
Q 2775 1125 3112 1675 
Q 3450 2225 3500 2675 
Q 3225 2650 3125 2625 
Q 3025 2600 2800 2525 
Q 2725 2825 2650 3025 
Q 3075 3075 3637 3587 
Q 4200 4100 4650 4575 
L 3700 4575 
Q 3150 4575 2675 4550 
L 2675 5025 
Q 3125 5000 3675 5000 
L 5625 5000 
z
M 1200 4375 
Q 1200 4650 1175 5075 
L 1700 5075 
Q 1675 4625 1675 4375 
L 1675 3450 
Q 2050 3450 2450 3475 
L 2450 3025 
Q 2000 3050 1675 3050 
L 1675 1225 
Q 1950 1325 2500 1575 
Q 2500 1350 2525 1125 
Q 2100 925 1500 687 
Q 900 450 500 225 
Q 400 450 200 725 
Q 650 850 1200 1050 
L 1200 3050 
Q 650 3050 325 3025 
L 325 3475 
Q 750 3450 1200 3450 
L 1200 4375 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-666f" d="M 5200 5100 
Q 5175 4700 5175 4150 
Q 5175 3625 5225 3225 
L 1175 3225 
Q 1200 3625 1200 4150 
Q 1200 4700 1175 5100 
L 5200 5100 
z
M 5125 2075 
Q 5100 1675 5100 1375 
Q 5100 1075 5125 900 
L 3525 900 
L 3525 -175 
Q 3525 -475 3312 -575 
Q 3100 -675 2575 -700 
Q 2550 -425 2375 -100 
Q 2800 -175 2937 -137 
Q 3075 -100 3050 100 
L 3050 900 
L 1350 900 
Q 1375 1125 1375 1450 
Q 1375 1800 1350 2075 
L 5125 2075 
z
M 3325 3200 
Q 3400 2950 3500 2750 
L 5200 2750 
Q 5625 2750 6050 2800 
L 6050 2350 
Q 5625 2375 5200 2375 
L 1175 2375 
Q 850 2375 350 2350 
L 350 2775 
Q 850 2750 1175 2750 
L 2900 2750 
Q 2875 2900 2800 3125 
Q 3100 3175 3325 3200 
z
M 4650 1250 
L 4650 1700 
L 1825 1700 
L 1825 1250 
L 4650 1250 
z
M 4700 4300 
L 4700 4700 
L 1675 4700 
L 1675 4300 
L 4700 4300 
z
M 4700 3600 
L 4700 3925 
L 1675 3925 
L 1675 3600 
L 4700 3600 
z
M 4325 725 
Q 4600 575 4987 387 
Q 5375 200 5850 -50 
Q 5700 -275 5550 -550 
Q 5325 -350 4925 -125 
Q 4525 100 4075 275 
L 4325 725 
z
M 2450 250 
Q 2225 175 1812 -100 
Q 1400 -375 1000 -600 
Q 850 -375 700 -175 
Q 975 -25 1350 187 
Q 1725 400 2075 700 
Q 2250 500 2450 250 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-521d" d="M 5675 550 
Q 5650 -175 5362 -337 
Q 5075 -500 4425 -575 
Q 4400 -275 4175 50 
Q 4850 50 5012 125 
Q 5175 200 5200 675 
L 5375 4175 
L 4300 4175 
Q 4275 3450 4225 2800 
Q 4175 2150 4025 1575 
Q 3875 1000 3562 450 
Q 3250 -100 2725 -600 
Q 2550 -425 2250 -275 
Q 2750 125 3037 562 
Q 3325 1000 3487 1475 
Q 3650 1950 3725 2625 
Q 3800 3300 3825 4175 
Q 3150 4175 2800 4150 
L 2800 4650 
Q 3375 4625 3825 4625 
L 5875 4625 
L 5675 550 
z
M 2575 3900 
Q 2475 3650 2312 3287 
Q 2150 2925 1900 2525 
Q 2050 2375 2200 2250 
Q 2375 2425 2725 2925 
L 3125 2525 
Q 2950 2400 2812 2275 
Q 2675 2150 2500 1975 
Q 2800 1675 3025 1425 
Q 2775 1250 2600 1050 
Q 2350 1425 2225 1562 
Q 2100 1700 1875 1925 
L 1875 175 
Q 1875 -200 1900 -600 
L 1375 -600 
Q 1400 -50 1400 175 
L 1400 1925 
Q 1100 1550 525 1075 
Q 350 1350 200 1525 
Q 675 1800 1112 2287 
Q 1550 2775 1875 3450 
L 1325 3450 
Q 900 3450 550 3425 
L 550 3925 
Q 900 3900 1300 3900 
L 2575 3900 
z
M 1750 5225 
Q 1975 4825 2175 4400 
Q 1975 4325 1700 4175 
Q 1525 4650 1300 5050 
Q 1575 5150 1750 5225 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-59cb" d="M 1750 5075 
Q 1650 4900 1587 4587 
Q 1525 4275 1475 3875 
L 2550 3875 
Q 2400 2150 1950 1100 
Q 2275 850 2725 425 
Q 2550 275 2375 50 
Q 2000 500 1750 675 
Q 1525 275 1250 -62 
Q 975 -400 700 -675 
Q 600 -525 250 -350 
Q 575 -125 862 200 
Q 1150 525 1375 975 
Q 800 1400 500 1550 
Q 875 2925 950 3425 
Q 650 3425 325 3400 
L 325 3900 
Q 675 3875 1025 3875 
Q 1175 4825 1175 5225 
Q 1550 5125 1750 5075 
z
M 5725 2050 
Q 5700 1650 5700 650 
Q 5700 -325 5725 -650 
L 5200 -650 
L 5200 -100 
L 3450 -100 
L 3450 -675 
L 2950 -675 
Q 2975 -300 2975 675 
Q 2975 1650 2950 2050 
L 5725 2050 
z
M 4400 4950 
Q 3750 3875 3250 3150 
Q 3875 3150 4250 3175 
Q 4625 3200 5200 3225 
Q 4875 3675 4625 3900 
Q 4825 4000 5025 4225 
Q 5750 3300 6125 2850 
Q 6000 2775 5725 2550 
Q 5575 2775 5450 2875 
Q 4575 2825 3937 2762 
Q 3300 2700 2825 2575 
Q 2775 2800 2600 3100 
Q 2875 3225 3237 3850 
Q 3600 4475 3825 5200 
Q 4025 5050 4400 4950 
z
M 5200 325 
L 5200 1600 
L 3450 1600 
L 3450 325 
L 5200 325 
z
M 1025 1775 
Q 1300 1550 1575 1400 
Q 1725 1725 1875 2337 
Q 2025 2950 2050 3425 
L 1400 3425 
Q 1300 2950 1025 1775 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-573a"/>
     <use xlink:href="#SimHei-666f" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-521d" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-59cb" transform="translate(300 0)"/>
     <use xlink:href="#SimHei-8ddd" transform="translate(400 0)"/>
     <use xlink:href="#SimHei-79bb" transform="translate(500 0)"/>
     <use xlink:href="#SimHei-20" transform="translate(600 0)"/>
     <use xlink:href="#SimHei-31" transform="translate(650 0)"/>
     <use xlink:href="#SimHei-35" transform="translate(700 0)"/>
     <use xlink:href="#SimHei-2e" transform="translate(750 0)"/>
     <use xlink:href="#SimHei-30" transform="translate(800 0)"/>
     <use xlink:href="#SimHei-20" transform="translate(850 0)"/>
     <use xlink:href="#SimHei-6d" transform="translate(900 0)"/>
    </g>
   </g>
   <g id="text_37">
    <!-- t=0 -->
    <g transform="translate(277.44 769.728) scale(0.16 -0.16)">
     <use xlink:href="#SimHei-74"/>
     <use xlink:href="#SimHei-3d" transform="translate(50 0)"/>
     <use xlink:href="#SimHei-30" transform="translate(100 0)"/>
    </g>
   </g>
   <g id="text_38">
    <!-- 发现危险 -->
    <g transform="translate(261.44 794.064) scale(0.14 -0.14)">
     <defs>
      <path id="SimHei-53d1" d="M 3475 4925 
Q 3350 4650 3275 4362 
Q 3200 4075 3075 3575 
L 5075 3575 
Q 5325 3575 5825 3600 
L 5825 3125 
Q 5325 3150 5075 3150 
L 2975 3150 
Q 2825 2775 2650 2400 
L 5300 2400 
Q 4825 1250 3975 475 
Q 4650 0 5950 -150 
Q 5775 -350 5650 -650 
Q 5150 -550 4625 -362 
Q 4100 -175 3600 175 
Q 3200 -75 2625 -312 
Q 2050 -550 1450 -700 
Q 1400 -450 1200 -225 
Q 1750 -150 2300 37 
Q 2850 225 3275 500 
Q 2850 925 2325 1725 
Q 2050 1275 1625 850 
Q 1200 425 700 75 
Q 600 250 300 450 
Q 675 650 1037 975 
Q 1400 1300 1800 1837 
Q 2200 2375 2475 3150 
Q 1825 3150 1475 3125 
Q 1125 3100 950 3025 
Q 875 3250 750 3500 
Q 1000 3550 1112 3875 
Q 1225 4200 1350 4875 
Q 1550 4775 1900 4675 
Q 1775 4425 1675 4162 
Q 1575 3900 1450 3575 
L 2600 3575 
Q 2800 4375 2900 5100 
Q 3175 4950 3475 4925 
z
M 2675 2000 
Q 3025 1375 3650 750 
Q 4200 1200 4625 2000 
L 2675 2000 
z
M 4400 5075 
Q 4825 4600 5250 4100 
Q 5075 4000 4825 3775 
Q 4425 4350 4050 4750 
Q 4225 4875 4400 5075 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-73b0" d="M 4650 3850 
Q 4550 3575 4512 2975 
Q 4475 2375 4350 1700 
L 4850 1700 
Q 4825 1450 4825 1025 
L 4825 225 
Q 4825 -150 4962 -175 
Q 5100 -200 5275 -187 
Q 5450 -175 5525 -12 
Q 5600 150 5625 575 
Q 5875 425 6175 375 
Q 6050 -150 5925 -337 
Q 5800 -525 5400 -575 
L 4900 -575 
Q 4350 -575 4350 -100 
L 4350 1650 
Q 4125 850 3700 312 
Q 3275 -225 2425 -675 
Q 2300 -475 2000 -325 
Q 2800 0 3175 387 
Q 3550 775 3775 1375 
Q 4000 1975 4037 2625 
Q 4075 3275 4050 3925 
Q 4375 3875 4650 3850 
z
M 1850 4625 
Q 2125 4625 2525 4650 
L 2525 4175 
Q 2125 4200 1675 4200 
L 1675 2825 
Q 2025 2825 2425 2850 
L 2425 2375 
Q 2025 2400 1675 2400 
L 1675 875 
Q 2100 975 2550 1125 
Q 2525 850 2525 650 
Q 1700 450 1200 275 
Q 700 100 450 -25 
Q 375 325 250 575 
Q 650 625 1225 750 
L 1225 2400 
Q 825 2400 450 2375 
L 450 2850 
Q 825 2825 1225 2825 
L 1225 4200 
Q 775 4200 425 4175 
L 425 4650 
Q 725 4625 1000 4625 
L 1850 4625 
z
M 5750 4875 
Q 5725 4475 5725 4075 
L 5725 2400 
Q 5725 1925 5750 1575 
L 5275 1575 
L 5275 4475 
L 3375 4475 
L 3375 1550 
L 2900 1550 
Q 2925 1975 2925 2500 
L 2925 3875 
Q 2925 4425 2900 4875 
L 5750 4875 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5371" d="M 2850 5050 
Q 2650 4950 2550 4812 
Q 2450 4675 2275 4525 
L 4800 4525 
L 4550 4025 
Q 4400 3725 4275 3425 
L 5000 3425 
Q 5500 3425 5900 3450 
L 5900 2975 
Q 5525 3000 5025 3000 
L 1675 3000 
Q 1675 2175 1612 1612 
Q 1550 1050 1412 562 
Q 1275 75 800 -600 
Q 600 -400 250 -275 
Q 800 300 987 962 
Q 1175 1625 1187 2262 
Q 1200 2900 1175 3375 
Q 850 3100 650 2975 
Q 475 3225 200 3375 
Q 750 3650 1250 4112 
Q 1750 4575 2150 5300 
Q 2425 5150 2850 5050 
z
M 5050 2450 
Q 5025 2175 5012 1875 
Q 5000 1575 4962 1237 
Q 4925 900 4625 812 
Q 4325 725 3875 650 
Q 3800 950 3675 1200 
Q 4400 1200 4462 1325 
Q 4525 1450 4525 2050 
L 2825 2050 
L 2825 300 
Q 2825 -75 3250 -50 
L 4950 -50 
Q 5125 -50 5200 75 
Q 5275 200 5325 650 
Q 5575 425 5875 375 
Q 5750 -75 5650 -275 
Q 5550 -475 5100 -475 
L 2900 -475 
Q 2375 -450 2350 0 
L 2350 1450 
Q 2350 2000 2325 2450 
L 5050 2450 
z
M 3725 3425 
Q 3925 3850 4025 4125 
L 1950 4125 
Q 1550 3700 1250 3425 
L 3725 3425 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-9669" d="M 2325 4875 
Q 2175 4525 2025 3975 
Q 1875 3425 1675 2850 
Q 2025 2350 2137 1987 
Q 2250 1625 2250 1300 
Q 2250 975 2000 800 
Q 1750 625 1400 550 
Q 1325 850 1175 1075 
Q 1650 1075 1712 1250 
Q 1775 1425 1712 1775 
Q 1650 2125 1225 2750 
Q 1600 3925 1700 4475 
L 1000 4475 
L 1000 -650 
L 525 -650 
Q 550 0 550 550 
L 550 3750 
Q 550 4250 525 4875 
L 2325 4875 
z
M 4225 4875 
Q 4675 4100 5112 3737 
Q 5550 3375 6200 3050 
Q 5950 2875 5825 2600 
Q 5425 2800 4975 3175 
L 4975 2750 
Q 4675 2775 4350 2775 
L 3675 2775 
Q 3275 2775 2925 2750 
L 2925 3025 
Q 2650 2725 2350 2425 
Q 2200 2675 1925 2825 
Q 2350 3075 2875 3725 
Q 3400 4375 3700 5250 
Q 4050 5100 4350 5025 
L 4225 4875 
z
M 5500 2100 
Q 5350 1850 5150 1262 
Q 4950 675 4775 25 
Q 5425 25 5950 50 
L 5950 -400 
Q 5525 -375 5100 -375 
L 3100 -375 
Q 2475 -375 2100 -400 
L 2100 50 
Q 2550 25 3125 25 
L 4325 25 
Q 4600 875 4712 1387 
Q 4825 1900 4850 2275 
Q 5125 2175 5500 2100 
z
M 4950 3200 
Q 4450 3675 3950 4550 
Q 3550 3825 3050 3200 
L 4950 3200 
z
M 3950 2250 
Q 4125 1625 4275 775 
Q 4050 775 3800 725 
Q 3625 1750 3500 2150 
Q 3750 2175 3950 2250 
z
M 2950 2075 
Q 3125 1425 3350 525 
Q 3125 500 2925 400 
Q 2825 975 2525 1925 
Q 2750 2000 2950 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-53d1"/>
     <use xlink:href="#SimHei-73b0" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-5371" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-9669" transform="translate(300 0)"/>
    </g>
   </g>
   <g id="text_39">
    <!-- t=0.5s -->
    <g transform="translate(477.12 769.728) scale(0.16 -0.16)">
     <use xlink:href="#SimHei-74"/>
     <use xlink:href="#SimHei-3d" transform="translate(50 0)"/>
     <use xlink:href="#SimHei-30" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-2e" transform="translate(150 0)"/>
     <use xlink:href="#SimHei-35" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-73" transform="translate(250 0)"/>
    </g>
   </g>
   <g id="text_40">
    <!-- 开始制动 -->
    <g transform="translate(473.12 794.064) scale(0.14 -0.14)">
     <defs>
      <path id="SimHei-5f00" d="M 2200 4450 
Q 1450 4450 800 4425 
L 800 4925 
Q 1425 4900 1950 4900 
L 4650 4900 
Q 5150 4900 5725 4925 
L 5725 4425 
Q 5175 4450 4500 4450 
L 4500 2600 
L 4975 2600 
Q 5475 2600 6050 2625 
L 6050 2125 
Q 5475 2175 4975 2175 
L 4500 2175 
L 4500 825 
Q 4500 200 4525 -575 
L 3950 -575 
Q 3975 200 3975 825 
L 3975 2175 
L 2725 2175 
Q 2650 1700 2525 1287 
Q 2400 875 2125 450 
Q 1850 25 1100 -575 
Q 900 -350 500 -150 
Q 1000 50 1362 412 
Q 1725 775 1937 1262 
Q 2150 1750 2175 2175 
L 1600 2175 
Q 950 2175 350 2100 
L 350 2625 
Q 975 2600 1600 2600 
L 2200 2600 
L 2200 4450 
z
M 3975 2600 
L 3975 4450 
L 2725 4450 
L 2725 2600 
L 3975 2600 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-5f00"/>
     <use xlink:href="#SimHei-59cb" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-5236" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-52a8" transform="translate(300 0)"/>
    </g>
   </g>
   <g id="text_41">
    <!-- 完全停止 -->
    <g transform="translate(892.48 769.728) scale(0.16 -0.16)">
     <defs>
      <path id="SimHei-5b8c" d="M 5125 2250 
Q 5600 2250 6025 2275 
L 6025 1800 
Q 5600 1825 5125 1825 
L 4050 1825 
L 4050 250 
Q 4050 25 4237 12 
Q 4425 0 4750 0 
Q 5100 0 5237 75 
Q 5375 150 5425 675 
Q 5775 475 6050 425 
Q 5900 -75 5712 -250 
Q 5525 -425 5300 -450 
L 4050 -450 
Q 3525 -425 3525 25 
L 3525 1825 
L 2625 1825 
Q 2600 1225 2400 725 
Q 2200 225 1700 -150 
Q 1200 -525 700 -700 
Q 500 -450 250 -250 
Q 925 -50 1300 212 
Q 1675 475 1837 800 
Q 2000 1125 2075 1825 
L 1300 1825 
Q 750 1825 350 1800 
L 350 2275 
Q 775 2250 1300 2250 
L 5125 2250 
z
M 2600 5000 
Q 2775 5100 3050 5275 
Q 3375 4850 3550 4500 
L 5900 4500 
Q 5850 4150 5850 3850 
Q 5850 3575 5900 3300 
L 5350 3300 
L 5350 4075 
L 1025 4075 
L 1025 3300 
L 450 3300 
Q 475 3625 475 3925 
Q 475 4225 450 4500 
L 2925 4500 
Q 2825 4675 2600 5000 
z
M 3900 3375 
Q 4375 3375 4800 3400 
L 4800 2900 
Q 4375 2925 3900 2925 
L 2350 2925 
Q 1875 2925 1425 2900 
L 1425 3400 
Q 1875 3375 2350 3375 
L 3900 3375 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5168" d="M 3475 4825 
Q 4350 3425 6125 3025 
Q 5900 2750 5825 2450 
Q 5325 2650 4975 2875 
L 4975 2475 
Q 4700 2500 4100 2500 
L 3500 2500 
L 3500 1500 
L 4450 1500 
Q 4975 1500 5300 1525 
L 5300 1075 
Q 4975 1100 4450 1100 
L 3500 1100 
L 3500 -25 
L 4475 -25 
Q 5225 -25 5700 25 
L 5700 -500 
Q 5250 -425 4525 -425 
L 1775 -425 
Q 1025 -425 650 -450 
L 650 0 
Q 1025 -25 1775 -25 
L 3000 -25 
L 3000 1100 
L 2300 1100 
Q 1450 1100 1175 1075 
L 1175 1525 
Q 1450 1500 2275 1500 
L 3000 1500 
L 3000 2500 
L 2500 2500 
Q 1725 2500 1500 2475 
L 1500 2875 
Q 1275 2700 650 2350 
Q 500 2650 300 2875 
Q 900 3025 1700 3625 
Q 2500 4225 2975 5200 
Q 3275 5075 3600 4950 
L 3475 4825 
z
M 1650 2950 
Q 1950 2925 2525 2925 
L 4025 2925 
Q 4525 2925 4800 2950 
Q 3725 3500 3200 4550 
Q 2525 3525 1650 2950 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-505c" d="M 5400 3900 
Q 5375 3600 5375 3225 
Q 5375 2875 5400 2675 
L 2525 2675 
Q 2550 2950 2550 3275 
Q 2550 3600 2525 3900 
L 5400 3900 
z
M 1925 5075 
Q 1750 4925 1525 4075 
L 1525 575 
Q 1525 25 1550 -525 
L 1075 -525 
Q 1100 0 1100 550 
L 1100 3250 
Q 925 2900 600 2300 
Q 375 2550 200 2675 
Q 425 2975 837 3775 
Q 1250 4575 1350 5250 
L 1925 5075 
z
M 4775 1475 
Q 5125 1475 5475 1525 
L 5475 1075 
Q 5125 1100 4800 1100 
L 4300 1100 
L 4300 -50 
Q 4275 -425 4025 -500 
Q 3775 -575 3350 -625 
Q 3325 -325 3150 -50 
Q 3575 -75 3712 -50 
Q 3850 -25 3850 150 
L 3850 1100 
L 3275 1100 
Q 2900 1100 2550 1075 
L 2550 1525 
Q 2900 1475 3300 1475 
L 4775 1475 
z
M 6100 2350 
Q 6075 2150 6087 1912 
Q 6100 1675 6100 1525 
L 5675 1525 
L 5675 1975 
L 2325 1975 
L 2325 1525 
L 1875 1525 
Q 1900 1750 1900 1950 
Q 1900 2175 1875 2350 
L 6100 2350 
z
M 4150 5300 
Q 4250 5025 4350 4675 
L 5075 4675 
Q 5650 4675 6000 4700 
L 6000 4275 
Q 5600 4300 5075 4300 
L 2925 4300 
Q 2525 4300 2125 4275 
L 2125 4700 
Q 2500 4675 2925 4675 
L 3800 4675 
Q 3725 4950 3625 5150 
Q 3875 5200 4150 5300 
z
M 4950 3050 
L 4950 3525 
L 2975 3525 
L 2975 3050 
L 4950 3050 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6b62" d="M 1875 3700 
Q 1850 3250 1850 2825 
L 1850 250 
L 3150 250 
L 3150 4100 
Q 3150 4575 3125 5050 
L 3675 5050 
Q 3650 4600 3650 4100 
L 3650 3250 
L 4650 3250 
Q 5075 3250 5600 3275 
L 5600 2775 
Q 5075 2800 4650 2800 
L 3650 2800 
L 3650 250 
L 5025 250 
Q 5525 250 6050 275 
L 6050 -225 
Q 5525 -200 5025 -200 
L 1375 -200 
Q 850 -200 325 -225 
L 325 275 
Q 725 250 1325 250 
L 1325 2825 
Q 1325 3250 1300 3700 
L 1875 3700 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-5b8c"/>
     <use xlink:href="#SimHei-5168" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-505c" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-6b62" transform="translate(300 0)"/>
    </g>
   </g>
   <g id="text_42">
    <!-- 反应阶段 -->
    <g transform="translate(363.28 818.4) scale(0.16 -0.16)">
     <defs>
      <path id="SimHei-9636" d="M 2450 4850 
Q 2350 4575 2175 3962 
Q 2000 3350 1825 2800 
Q 2225 2225 2312 1912 
Q 2400 1600 2375 1250 
Q 2350 900 2137 725 
Q 1925 550 1400 475 
Q 1300 925 1200 1075 
Q 1725 1025 1837 1225 
Q 1950 1425 1875 1775 
Q 1800 2125 1350 2725 
Q 1700 3825 1825 4400 
L 975 4400 
L 975 -500 
L 500 -500 
Q 525 175 525 675 
L 525 3650 
Q 525 4225 500 4850 
L 2450 4850 
z
M 4250 4825 
Q 4600 4100 5187 3637 
Q 5775 3175 6200 3025 
Q 5950 2775 5800 2500 
Q 4975 3075 4600 3512 
Q 4225 3950 4025 4400 
Q 3850 4025 3512 3487 
Q 3175 2950 2625 2350 
Q 2425 2575 2175 2700 
Q 2700 3075 3150 3787 
Q 3600 4500 3850 5275 
Q 4050 5150 4375 5075 
Q 4275 4900 4250 4825 
z
M 3750 2700 
Q 3650 2325 3575 1512 
Q 3500 700 3250 225 
Q 3000 -250 2650 -575 
Q 2425 -400 2175 -300 
Q 2550 -25 2762 337 
Q 2975 700 3087 1200 
Q 3200 1700 3200 2800 
Q 3350 2725 3750 2700 
z
M 5025 2775 
Q 4975 2425 4975 2025 
L 4975 350 
Q 4975 100 5000 -525 
L 4475 -525 
Q 4500 150 4500 375 
L 4500 2025 
Q 4500 2375 4475 2775 
L 5025 2775 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6bb5" d="M 5500 2475 
Q 5350 1775 5100 1237 
Q 4850 700 4600 400 
Q 4825 225 5250 87 
Q 5675 -50 6125 -125 
Q 5950 -275 5825 -650 
Q 5150 -450 4825 -287 
Q 4500 -125 4225 75 
Q 3825 -175 3450 -337 
Q 3075 -500 2625 -650 
Q 2525 -400 2325 -225 
Q 2675 -150 3100 0 
Q 3525 150 3925 425 
Q 3725 675 3525 1150 
Q 3325 1600 3225 2075 
Q 3050 2075 2900 2075 
L 2900 2500 
Q 3375 2475 3800 2475 
L 5500 2475 
z
M 2650 4625 
Q 2300 4550 1887 4475 
Q 1475 4400 1300 4375 
L 1300 3550 
Q 2050 3550 2525 3600 
L 2525 3125 
Q 2050 3150 1300 3150 
L 1300 2350 
Q 2050 2350 2525 2375 
L 2525 1900 
Q 2050 1925 1300 1925 
L 1300 975 
Q 1950 1075 2625 1250 
Q 2600 1000 2600 775 
Q 1900 675 1300 525 
L 1300 100 
Q 1300 -200 1325 -625 
L 800 -625 
Q 825 -200 825 100 
L 825 425 
Q 575 375 350 300 
Q 300 650 250 825 
Q 500 850 825 900 
L 825 3800 
Q 825 4225 800 4725 
Q 1100 4725 1575 4825 
Q 2050 4925 2375 5125 
Q 2475 4850 2650 4625 
z
M 5100 4975 
Q 5075 4600 5075 4275 
L 5075 3650 
Q 5075 3425 5237 3387 
Q 5400 3350 5975 3400 
Q 5875 3225 5850 2950 
L 5075 2950 
Q 4650 2950 4625 3375 
L 4625 4575 
L 3775 4575 
Q 3775 3925 3675 3525 
Q 3575 3125 3200 2725 
Q 3025 2900 2800 3050 
Q 3275 3400 3300 3937 
Q 3325 4475 3300 4975 
L 5100 4975 
z
M 3675 2100 
Q 3775 1675 3962 1287 
Q 4150 900 4275 775 
Q 4450 950 4625 1312 
Q 4800 1675 4900 2100 
L 3800 2100 
L 3675 2100 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-53cd"/>
     <use xlink:href="#SimHei-5e94" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-9636" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-6bb5" transform="translate(300 0)"/>
    </g>
   </g>
   <g id="text_43">
    <!-- 制动阶段 -->
    <g transform="translate(680.8 818.4) scale(0.16 -0.16)">
     <use xlink:href="#SimHei-5236"/>
     <use xlink:href="#SimHei-52a8" transform="translate(100 0)"/>
     <use xlink:href="#SimHei-9636" transform="translate(200 0)"/>
     <use xlink:href="#SimHei-6bb5" transform="translate(300 0)"/>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p64a9a70894">
   <rect x="7.2" y="7.2" width="1693.44" height="973.44"/>
  </clipPath>
 </defs>
</svg>
