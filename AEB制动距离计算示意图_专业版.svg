<?xml version="1.0" encoding="UTF-8"?>
<svg width="1500" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "SimSun", "宋体", serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: "SimSun", "宋体", serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: "SimSun", "宋体", serif; font-size: 16px; fill: #2c3e50; line-height: 1.5; }
      .formula { font-family: "Times New Roman", serif; font-size: 16px; fill: #d32f2f; font-weight: bold; }
      .result { font-family: "SimSun", "宋体", serif; font-size: 16px; fill: #27ae60; font-weight: bold; }
      .warning { font-family: "SimSun", "宋体", serif; font-size: 18px; fill: #d32f2f; font-weight: bold; }
      .car { fill: #2196f3; stroke: #1976d2; stroke-width: 2; }
      .obstacle { fill: #f44336; stroke: #d32f2f; stroke-width: 2; }
      .distance-line { stroke: #ff9800; stroke-width: 3; }
      .arrow { fill: #ff9800; }
      .timeline { stroke: #666; stroke-width: 2; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1500" height="800" fill="#fafafa" stroke="#ddd" stroke-width="2"/>
  
  <!-- 标题 -->
  <text x="750" y="40" text-anchor="middle" class="title">AEB制动距离计算示意图</text>
  
  <!-- 已知参数框 -->
  <rect x="50" y="80" width="320" height="200" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="10"/>
  <text x="210" y="105" text-anchor="middle" class="section-title">已知参数</text>
  <text x="70" y="135" class="text">测试车速</text>
  <text x="70" y="155" class="formula">V₀ = 50 km/h</text>
  <text x="70" y="180" class="text">系统反应时间</text>
  <text x="70" y="200" class="formula">t = 0.5 s</text>
  <text x="70" y="225" class="text">最大减速度</text>
  <text x="70" y="245" class="formula">a = 9.8 m/s²</text>
  <text x="70" y="270" class="text">场景初始距离 = 15.0 m</text>
  
  <!-- 计算步骤框 -->
  <rect x="400" y="80" width="700" height="200" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="10"/>
  <text x="750" y="105" text-anchor="middle" class="section-title">计算步骤</text>
  
  <!-- 步骤一 -->
  <text x="420" y="135" class="text">步骤一：单位换算</text>
  <text x="440" y="155" class="formula">V₀ = 50 × (1000/3600) = 13.89 m/s</text>
  
  <!-- 步骤二 -->
  <text x="420" y="185" class="text">步骤二：计算反应距离</text>
  <text x="440" y="205" class="formula">D</text>
  <text x="455" y="210" class="formula" font-size="12">反应</text>
  <text x="495" y="205" class="formula"> = V₀ × t = 13.89 × 0.5 = 6.945 m</text>
  
  <!-- 步骤三 -->
  <text x="420" y="235" class="text">步骤三：计算纯制动距离</text>
  <text x="440" y="255" class="formula">D</text>
  <text x="455" y="260" class="formula" font-size="12">制动</text>
  <text x="495" y="255" class="formula"> = V₀²/(2a) = 13.89²/(2×9.8) = 9.84 m</text>
  
  <!-- 步骤四 -->
  <text x="420" y="285" class="text">步骤四：计算总制动距离</text>
  <text x="440" y="305" class="formula">D</text>
  <text x="455" y="310" class="formula" font-size="12">总</text>
  <text x="475" y="305" class="formula"> = D</text>
  <text x="495" y="310" class="formula" font-size="12">反应</text>
  <text x="525" y="305" class="formula"> + D</text>
  <text x="550" y="310" class="formula" font-size="12">制动</text>
  <text x="585" y="305" class="formula"> = 6.945 + 9.84 = 16.785 m</text>
  
  <!-- 关键公式框 -->
  <rect x="1130" y="80" width="320" height="200" fill="#e8f5e8" stroke="#388e3c" stroke-width="2" rx="10"/>
  <text x="1290" y="105" text-anchor="middle" class="section-title">关键公式</text>
  <text x="1150" y="135" class="text">单位换算：</text>
  <text x="1150" y="155" class="formula">V(m/s) = V(km/h) × 1000/3600</text>
  <text x="1150" y="185" class="text">反应距离：</text>
  <text x="1150" y="205" class="formula">D</text>
  <text x="1165" y="210" class="formula" font-size="12">反应</text>
  <text x="1195" y="205" class="formula"> = V₀ × t</text>
  <text x="1295" y="210" class="formula" font-size="12">反应</text>
  <text x="1150" y="235" class="text">制动距离：</text>
  <text x="1150" y="255" class="formula">D</text>
  <text x="1165" y="260" class="formula" font-size="12">制动</text>
  <text x="1195" y="255" class="formula"> = V₀²/(2a)</text>
  
  <!-- 结论框 -->
  <rect x="400" y="300" width="700" height="50" fill="#ffebee" stroke="#d32f2f" stroke-width="3" rx="10"/>
  <text x="750" y="335" text-anchor="middle" class="warning">结论：16.785 m > 15.0 m，测试不达标！</text>
  
  <!-- 制动过程示意图标题 -->
  <text x="750" y="390" text-anchor="middle" class="section-title">制动过程示意图</text>
  
  <!-- 地面线 -->
  <line x1="100" y1="500" x2="1400" y2="500" stroke="#333" stroke-width="4"/>
  
  <!-- 车辆 -->
  <rect x="120" y="460" width="80" height="40" class="car" rx="5"/>
  <circle cx="140" cy="510" r="15" class="car"/>
  <circle cx="180" cy="510" r="15" class="car"/>
  <text x="160" y="485" text-anchor="middle" class="text" fill="white" font-size="14" font-weight="bold">车辆</text>
  
  <!-- 障碍物 -->
  <rect x="1200" y="450" width="60" height="50" class="obstacle" rx="5"/>
  <text x="1230" y="480" text-anchor="middle" class="text" fill="white" font-size="14" font-weight="bold">障碍物</text>
  
  <!-- 距离标注 -->
  <!-- 反应距离 -->
  <line x1="200" y1="430" x2="450" y2="430" class="distance-line"/>
  <polygon points="200,425 210,430 200,435" class="arrow"/>
  <polygon points="450,425 440,430 450,435" class="arrow"/>
  <text x="325" y="415" text-anchor="middle" class="result">反应距离</text>
  <text x="325" y="445" text-anchor="middle" class="result">6.945 m</text>
  
  <!-- 制动距离 -->
  <line x1="450" y1="410" x2="750" y2="410" class="distance-line"/>
  <polygon points="450,405 460,410 450,415" class="arrow"/>
  <polygon points="750,405 740,410 750,415" class="arrow"/>
  <text x="600" y="395" text-anchor="middle" class="result">制动距离</text>
  <text x="600" y="425" text-anchor="middle" class="result">9.84 m</text>
  
  <!-- 总距离 -->
  <line x1="200" y1="390" x2="750" y2="390" stroke="#d32f2f" stroke-width="4"/>
  <polygon points="200,385 210,390 200,395" fill="#d32f2f"/>
  <polygon points="750,385 740,390 750,395" fill="#d32f2f"/>
  <text x="475" y="375" text-anchor="middle" class="warning">总制动距离 16.785 m</text>
  
  <!-- 场景距离 -->
  <line x1="200" y1="530" x2="1200" y2="530" stroke="#27ae60" stroke-width="3" stroke-dasharray="10,5"/>
  <text x="700" y="550" text-anchor="middle" class="result">场景初始距离 15.0 m</text>
  
  <!-- 速度标注 -->
  <rect x="120" y="420" width="80" height="30" fill="#fff59d" stroke="#fbc02d" stroke-width="2" rx="5"/>
  <text x="160" y="435" text-anchor="middle" class="text" font-size="12">V₀=50km/h</text>
  <text x="160" y="445" text-anchor="middle" class="text" font-size="12">(13.89m/s)</text>
  
  <!-- 时间轴 -->
  <line x1="100" y1="600" x2="1400" y2="600" class="timeline"/>
  <line x1="200" y1="595" x2="200" y2="605" class="timeline"/>
  <line x1="450" y1="595" x2="450" y2="605" class="timeline"/>
  <line x1="750" y1="595" x2="750" y2="605" class="timeline"/>
  
  <text x="200" y="625" text-anchor="middle" class="text">t=0</text>
  <text x="200" y="645" text-anchor="middle" class="text">发现危险</text>
  <text x="450" y="625" text-anchor="middle" class="text">t=0.5s</text>
  <text x="450" y="645" text-anchor="middle" class="text">开始制动</text>
  <text x="750" y="625" text-anchor="middle" class="text">完全停止</text>
  
  <!-- 阶段标注 -->
  <rect x="220" y="670" width="210" height="40" fill="rgba(255, 152, 0, 0.3)" stroke="#ff9800" stroke-width="2" rx="5"/>
  <text x="325" y="695" text-anchor="middle" class="text" font-weight="bold">反应阶段</text>
  
  <rect x="470" y="670" width="260" height="40" fill="rgba(244, 67, 54, 0.3)" stroke="#f44336" stroke-width="2" rx="5"/>
  <text x="600" y="695" text-anchor="middle" class="text" font-weight="bold">制动阶段</text>
</svg>
