<?xml version="1.0" encoding="UTF-8"?>
<svg width="2000" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="reactionGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff9a9e;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#fad0c4;stop-opacity:0.9" />
    </linearGradient>
    <linearGradient id="brakingGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#a8edea;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#fed6e3;stop-opacity:0.9" />
    </linearGradient>
    <linearGradient id="cardGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="4" dy="4" stdDeviation="4" flood-color="#00000030"/>
    </filter>
    <filter id="lightShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#00000020"/>
    </filter>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="15" markerHeight="10" refX="14" refY="5" orient="auto">
      <polygon points="0 0, 15 5, 0 10" fill="#4facfe" />
    </marker>
    <marker id="redArrow" markerWidth="15" markerHeight="10" refX="14" refY="5" orient="auto">
      <polygon points="0 0, 15 5, 0 10" fill="#e74c3c" />
    </marker>
    
    <!-- 样式定义 -->
    <style>
      .title { 
        font-family: "Microsoft YaHei", "SimSun", serif; 
        font-size: 36px; 
        font-weight: bold; 
        fill: white; 
        text-anchor: middle;
      }
      .section-title { 
        font-family: "Microsoft YaHei", "SimSun", serif; 
        font-size: 22px; 
        font-weight: bold; 
        fill: #2c3e50; 
        text-anchor: middle;
      }
      .text { 
        font-family: "SimSun", serif; 
        font-size: 18px; 
        fill: #34495e; 
      }
      .formula { 
        font-family: "Consolas", "SimSun", monospace; 
        font-size: 18px; 
        fill: #8e44ad; 
        font-weight: bold; 
      }
      .result { 
        font-family: "Microsoft YaHei", "SimSun", serif; 
        font-size: 20px; 
        fill: #27ae60; 
        font-weight: bold; 
      }
      .highlight { 
        font-family: "Microsoft YaHei", "SimSun", serif; 
        font-size: 22px; 
        fill: #e74c3c; 
        font-weight: bold; 
      }
      .card { 
        fill: url(#cardGrad); 
        stroke: #e1e8ed; 
        stroke-width: 2; 
        filter: url(#shadow); 
        rx: 12; 
      }
      .header-card { 
        fill: url(#headerGrad); 
        stroke: none; 
        filter: url(#shadow); 
        rx: 15; 
      }
      .math-text {
        font-family: "Times New Roman", "SimSun", serif;
        font-size: 18px;
        fill: #2c3e50;
      }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="2000" height="1200" fill="#f5f7fa"/>
  
  <!-- 标题区域 -->
  <rect x="100" y="40" width="1800" height="90" class="header-card" />
  <text x="1000" y="95" class="title">AEB系统制动距离计算与安全性分析</text>
  
  <!-- 参数设定卡片 -->
  <rect x="120" y="170" width="420" height="280" class="card" />
  <rect x="135" y="185" width="390" height="45" fill="#4facfe" rx="6" />
  <text x="330" y="215" class="section-title" fill="white">测试参数设定</text>
  
  <g transform="translate(150, 260)">
    <circle cx="15" cy="0" r="6" fill="#4facfe" />
    <text x="35" y="6" class="text">初始车速：V₀ = 50 km/h</text>
    
    <circle cx="15" cy="35" r="6" fill="#4facfe" />
    <text x="35" y="41" class="text">系统反应时间：t = 0.5 s</text>
    
    <circle cx="15" cy="70" r="6" fill="#4facfe" />
    <text x="35" y="76" class="text">最大减速度：a = 9.8 m/s²</text>
    
    <circle cx="15" cy="105" r="6" fill="#4facfe" />
    <text x="35" y="111" class="text">场景初始距离：D = 15.0 m</text>
    
    <rect x="0" y="140" width="360" height="50" fill="#e8f4fd" stroke="#4facfe" stroke-width="2" rx="6" />
    <text x="180" y="170" class="section-title">计算目标：总制动距离</text>
  </g>
  
  <!-- 计算流程卡片 -->
  <rect x="580" y="170" width="800" height="280" class="card" />
  <rect x="595" y="185" width="770" height="45" fill="#8e44ad" rx="6" />
  <text x="980" y="215" class="section-title" fill="white">三步计算流程</text>
  
  <!-- 步骤1：单位换算 -->
  <rect x="620" y="260" width="220" height="80" fill="#f8f9fa" stroke="#4facfe" stroke-width="3" rx="8" />
  <text x="730" y="285" text-anchor="middle" class="text">步骤1：单位换算</text>
  <text x="730" y="310" text-anchor="middle" class="formula">V = 50 × (1000/3600)</text>
  <text x="730" y="330" text-anchor="middle" class="result">= 13.89 m/s</text>
  
  <!-- 步骤2：反应距离 -->
  <rect x="870" y="260" width="220" height="80" fill="#f8f9fa" stroke="#e74c3c" stroke-width="3" rx="8" />
  <text x="980" y="285" text-anchor="middle" class="text">步骤2：反应距离</text>
  <text x="980" y="310" text-anchor="middle" class="formula">D₁ = V × t</text>
  <text x="980" y="330" text-anchor="middle" class="result">= 6.945 m</text>
  
  <!-- 步骤3：制动距离 -->
  <rect x="1120" y="260" width="220" height="80" fill="#f8f9fa" stroke="#27ae60" stroke-width="3" rx="8" />
  <text x="1230" y="285" text-anchor="middle" class="text">步骤3：制动距离</text>
  <text x="1230" y="310" text-anchor="middle" class="formula">D₂ = V²/(2a)</text>
  <text x="1230" y="330" text-anchor="middle" class="result">= 9.84 m</text>
  
  <!-- 流程箭头 -->
  <path d="M 840 300 L 865 300" stroke="#4facfe" stroke-width="4" marker-end="url(#arrowhead)" />
  <path d="M 1090 300 L 1115 300" stroke="#4facfe" stroke-width="4" marker-end="url(#arrowhead)" />
  
  <!-- 详细公式展示 -->
  <text x="730" y="370" text-anchor="middle" class="math-text">50 km/h → m/s</text>
  <text x="980" y="370" text-anchor="middle" class="math-text">13.89 × 0.5 = 6.945</text>
  <text x="1230" y="370" text-anchor="middle" class="math-text">(13.89)²/(2×9.8) = 9.84</text>
  
  <!-- 结论分析卡片 -->
  <rect x="1420" y="170" width="460" height="280" class="card" />
  <rect x="1435" y="185" width="430" height="45" fill="#e74c3c" rx="6" />
  <text x="1650" y="215" class="section-title" fill="white">安全性评估结果</text>
  
  <!-- 距离对比 -->
  <rect x="1450" y="260" width="400" height="100" fill="#f8f9fa" stroke="#e1e8ed" stroke-width="2" rx="8" />
  <text x="1650" y="285" text-anchor="middle" class="section-title">距离对比分析</text>
  <text x="1470" y="315" class="text">所需制动距离：</text>
  <text x="1820" y="315" text-anchor="end" class="highlight">16.785 m</text>
  <text x="1470" y="340" class="text">可用安全距离：</text>
  <text x="1820" y="340" text-anchor="end" class="text">15.0 m</text>
  
  <!-- 结论 -->
  <rect x="1450" y="380" width="400" height="60" fill="#ffebee" stroke="#e74c3c" stroke-width="3" rx="8" />
  <text x="1650" y="405" text-anchor="middle" class="section-title">16.785 > 15.0</text>
  <text x="1650" y="425" text-anchor="middle" class="highlight">系统无法避免碰撞</text>
  
  <!-- 距离可视化主区域 -->
  <rect x="120" y="490" width="1760" height="280" class="card" />
  <rect x="135" y="505" width="1730" height="45" fill="#34495e" rx="6" />
  <text x="1000" y="535" class="section-title" fill="white">制动距离可视化分析图</text>
  
  <!-- 道路基线 -->
  <rect x="200" y="620" width="1600" height="12" fill="#7f8c8d" rx="6" />
  
  <!-- 车辆起始位置 -->
  <g transform="translate(180, 590)">
    <rect x="0" y="0" width="70" height="35" fill="#2c3e50" rx="5" />
    <rect x="8" y="8" width="20" height="19" fill="#3498db" rx="3" />
    <rect x="42" y="8" width="20" height="19" fill="#3498db" rx="3" />
    <circle cx="15" cy="42" r="8" fill="#34495e" />
    <circle cx="55" cy="42" r="8" fill="#34495e" />
  </g>
  <text x="215" y="580" text-anchor="middle" class="text">测试车辆</text>
  <text x="215" y="670" text-anchor="middle" class="text">V₀ = 50 km/h</text>
  
  <!-- 反应距离段 -->
  <rect x="250" y="605" width="347" height="35" fill="url(#reactionGrad)" rx="6" />
  <text x="423" y="595" text-anchor="middle" class="section-title">反应距离段</text>
  <text x="423" y="630" text-anchor="middle" class="highlight">6.945 m</text>
  <text x="423" y="655" text-anchor="middle" class="text">系统响应期间车辆继续前进</text>
  
  <!-- 制动距离段 -->
  <rect x="597" y="605" width="492" height="35" fill="url(#brakingGrad)" rx="6" />
  <text x="843" y="595" text-anchor="middle" class="section-title">制动距离段</text>
  <text x="843" y="630" text-anchor="middle" class="highlight">9.84 m</text>
  <text x="843" y="655" text-anchor="middle" class="text">制动系统作用至完全停止</text>
  
  <!-- 总距离标注 -->
  <path d="M 250 700 L 1089 700" stroke="#4facfe" stroke-width="4" marker-end="url(#arrowhead)" />
  <text x="669" y="690" text-anchor="middle" class="section-title">总制动距离</text>
  <text x="669" y="720" text-anchor="middle" class="highlight">D总 = 6.945 + 9.84 = 16.785 m</text>
  
  <!-- 障碍物 -->
  <g transform="translate(1200, 580)">
    <rect x="0" y="0" width="60" height="70" fill="#e74c3c" rx="6" />
    <rect x="8" y="8" width="44" height="12" fill="#c0392b" />
    <rect x="8" y="25" width="44" height="12" fill="#c0392b" />
    <rect x="8" y="42" width="44" height="12" fill="#c0392b" />
    <rect x="8" y="59" width="44" height="8" fill="#c0392b" />
  </g>
  <text x="1230" y="570" text-anchor="middle" class="text">静止障碍物</text>
  
  <!-- 场景可用距离标注 -->
  <path d="M 180 740 L 1200 740" stroke="#95a5a6" stroke-width="3" stroke-dasharray="8,8" />
  <text x="690" y="730" text-anchor="middle" class="text">测试场景可用距离：15.0 m</text>
  
  <!-- 碰撞点标注 -->
  <g transform="translate(1089, 620)">
    <circle cx="0" cy="6" r="8" fill="#e74c3c" />
    <text x="0" y="12" text-anchor="middle" class="text" fill="white" font-size="14">!</text>
  </g>
  <text x="1089" y="580" text-anchor="middle" class="highlight">实际停车点</text>
  <path d="M 1089 600 L 1200 600" stroke="#e74c3c" stroke-width="4" marker-end="url(#redArrow)" />
  <text x="1144" y="595" text-anchor="middle" class="text" fill="#e74c3c">碰撞区域</text>
  
  <!-- 公式原理说明卡片 -->
  <rect x="120" y="810" width="1760" height="320" class="card" />
  <rect x="135" y="825" width="1730" height="45" fill="#27ae60" rx="6" />
  <text x="1000" y="855" class="section-title" fill="white">核心公式与物理原理说明</text>
  
  <!-- 公式网格布局 -->
  <g transform="translate(180, 900)">
    <!-- 第一行公式 -->
    <rect x="0" y="0" width="400" height="60" fill="#f8f9fa" stroke="#27ae60" stroke-width="2" rx="8" />
    <text x="20" y="25" class="text">速度单位换算公式：</text>
    <text x="20" y="45" class="formula">V(m/s) = V(km/h) × (1000/3600)</text>
    
    <rect x="420" y="0" width="400" height="60" fill="#f8f9fa" stroke="#e74c3c" stroke-width="2" rx="8" />
    <text x="440" y="25" class="text">反应距离计算公式：</text>
    <text x="440" y="45" class="formula">D反应 = V × t反应</text>
    
    <rect x="840" y="0" width="400" height="60" fill="#f8f9fa" stroke="#8e44ad" stroke-width="2" rx="8" />
    <text x="860" y="25" class="text">制动距离计算公式：</text>
    <text x="860" y="45" class="formula">D制动 = V²/(2a)</text>
    
    <rect x="1260" y="0" width="400" height="60" fill="#f8f9fa" stroke="#4facfe" stroke-width="2" rx="8" />
    <text x="1280" y="25" class="text">总制动距离公式：</text>
    <text x="1280" y="45" class="formula">D总 = D反应 + D制动</text>
    
    <!-- 第二行说明 -->
    <rect x="0" y="80" width="1660" height="80" fill="#e8f4fd" stroke="#4facfe" stroke-width="2" rx="8" />
    <text x="830" y="110" text-anchor="middle" class="section-title">物理原理基础</text>
    <text x="30" y="135" class="text">基于运动学方程：v² = v₀² + 2as（其中终速度v=0，初速度v₀，加速度a为负值）</text>
    <text x="30" y="155" class="text">安全判定标准：当 D总 ≤ D场景 时，AEB系统能够成功避免碰撞事故</text>
  </g>
</svg>