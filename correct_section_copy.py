import asyncio
from playwright.async_api import async_playwright

class SectionCopyTool:
    def __init__(self):
        self.browser = None
        self.context = None
        self.page = None
        
    async def start_browser_and_wait_for_login(self):
        """启动浏览器并等待手动登录"""
        print('🚀 启动浏览器...')
        
        async with async_playwright() as p:
            self.browser = await p.chromium.launch(headless=False, slow_mo=800)
            self.context = await self.browser.new_context(viewport={'width': 1920, 'height': 1080})
            self.page = await self.context.new_page()
            
            # 导航到页面
            await self.page.goto('https://textbookeditor.lessonplan.cn/ed0b4d38-03c8-150b-edab-a4d423944978')
            print('✅ 已打开教材编辑器页面')
            
            # 等待用户手动登录并恢复内容
            print('⏳ 请在浏览器中：')
            print('   1. 手动完成登录')
            print('   2. 恢复您的内容')
            print('   3. 确保1.1节的内容完整显示')
            print('   4. 然后回到这里按回车继续...')
            input('准备好后按回车键继续...')
            
            return await self.execute_section_copying()
    
    async def find_complete_section_11(self):
        """查找完整的1.1节内容块"""
        print('🔍 查找完整的1.1节内容块...')
        
        # 等待页面完全加载
        await self.page.wait_for_timeout(3000)
        
        # 截图当前状态
        await self.page.screenshot(path='before_copy.png', full_page=True)
        print('📸 已保存当前页面截图: before_copy.png')
        
        # 尝试查找1.1节的完整容器
        section_selectors = [
            # 尝试查找包含1.1的标题元素的父容器
            'h1:has-text("1.1")',
            'h2:has-text("1.1")',
            'h3:has-text("1.1")',
            'div:has-text("1.1")',
            '[data-section="1.1"]',
            '.section:has-text("1.1")',
            '.chapter:has-text("1.1")',
        ]
        
        section_element = None
        section_container = None
        
        # 首先在主页面查找
        for selector in section_selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                for element in elements:
                    text = await element.text_content()
                    if text and '1.1' in text and len(text.strip()) > 10:  # 确保不是空元素
                        print(f'✅ 找到1.1节元素: {selector}')
                        print(f'📝 内容预览: {text[:100]}...')
                        section_element = element
                        
                        # 尝试找到包含完整内容的父容器
                        parent = await element.evaluate('el => el.parentElement')
                        if parent:
                            parent_text = await parent.text_content()
                            if len(parent_text) > len(text):  # 父容器包含更多内容
                                section_container = parent
                                print('📦 找到更完整的父容器')
                        break
                if section_element:
                    break
            except Exception as e:
                continue
        
        # 如果在主页面没找到，尝试在iframe中查找
        if not section_element:
            print('🔍 在iframe中查找1.1节...')
            iframes = await self.page.query_selector_all('iframe')
            print(f'🔍 找到 {len(iframes)} 个iframe')
            
            for i, iframe in enumerate(iframes):
                try:
                    frame = await iframe.content_frame()
                    if frame:
                        print(f'🔍 检查第 {i+1} 个iframe...')
                        
                        for selector in section_selectors:
                            try:
                                elements = await frame.query_selector_all(selector)
                                for element in elements:
                                    text = await element.text_content()
                                    if text and '1.1' in text and len(text.strip()) > 10:
                                        print(f'✅ 在iframe中找到1.1节: {selector}')
                                        print(f'📝 内容预览: {text[:100]}...')
                                        section_element = element
                                        
                                        # 查找父容器
                                        parent = await element.evaluate('el => el.parentElement')
                                        if parent:
                                            parent_text = await parent.text_content()
                                            if len(parent_text) > len(text):
                                                section_container = parent
                                        
                                        return frame, section_container or section_element
                            except Exception:
                                continue
                except Exception as e:
                    continue
        
        return self.page, section_container or section_element
    
    async def copy_complete_section(self, page_or_frame, section_element):
        """复制完整的1.1节内容"""
        print('📋 开始复制完整的1.1节内容...')
        
        try:
            # 点击1.1节元素
            await section_element.click()
            await page_or_frame.wait_for_timeout(1000)
            
            # 尝试选择整个内容块
            # 方法1: 三击选择段落
            await section_element.click(click_count=3)
            await page_or_frame.wait_for_timeout(500)
            
            # 方法2: 如果三击不够，尝试扩展选择
            await page_or_frame.keyboard.press('Control+Shift+End')
            await page_or_frame.wait_for_timeout(500)
            
            # 复制选中的内容
            await page_or_frame.keyboard.press('Control+C')
            await page_or_frame.wait_for_timeout(1000)
            print('✅ 已复制1.1节完整内容')
            
            return True
            
        except Exception as e:
            print(f'❌ 复制过程中出错: {e}')
            return False
    
    async def create_multiple_sections(self, page_or_frame, start_section=2, end_section=10):
        """创建多个节（1.2到1.10）"""
        print(f'📝 开始创建1.{start_section}到1.{end_section}节...')
        
        for section_num in range(start_section, end_section + 1):
            try:
                print(f'🔄 创建1.{section_num}节...')
                
                # 移动到文档末尾
                await page_or_frame.keyboard.press('Control+End')
                await page_or_frame.keyboard.press('Enter')
                await page_or_frame.keyboard.press('Enter')
                await page_or_frame.wait_for_timeout(500)
                
                # 粘贴内容
                await page_or_frame.keyboard.press('Control+V')
                await page_or_frame.wait_for_timeout(2000)
                
                # 查找刚粘贴的1.1并修改为当前节号
                try:
                    # 向上移动一些，然后查找最近的1.1
                    await page_or_frame.keyboard.press('Control+Home')
                    await page_or_frame.keyboard.press('Control+F')  # 打开查找
                    await page_or_frame.wait_for_timeout(500)
                    await page_or_frame.keyboard.type('1.1')
                    await page_or_frame.keyboard.press('Enter')
                    
                    # 查找最后一个1.1（刚粘贴的）
                    for _ in range(section_num):  # 跳过前面的1.1
                        await page_or_frame.keyboard.press('F3')  # 查找下一个
                        await page_or_frame.wait_for_timeout(300)
                    
                    await page_or_frame.keyboard.press('Escape')  # 关闭查找框
                    await page_or_frame.wait_for_timeout(500)
                    
                    # 选择1.1文本并替换
                    await page_or_frame.keyboard.press('Control+H')  # 打开替换
                    await page_or_frame.wait_for_timeout(500)
                    await page_or_frame.keyboard.type('1.1')  # 查找内容
                    await page_or_frame.keyboard.press('Tab')  # 移动到替换框
                    await page_or_frame.keyboard.type(f'1.{section_num}')  # 替换内容
                    await page_or_frame.keyboard.press('Alt+A')  # 全部替换
                    await page_or_frame.wait_for_timeout(500)
                    await page_or_frame.keyboard.press('Escape')  # 关闭替换框
                    
                    print(f'✅ 成功创建1.{section_num}节')
                    
                except Exception as e:
                    print(f'❌ 修改1.{section_num}节标题时出错: {e}')
                    # 如果自动替换失败，至少内容已经复制了
                    continue
                    
            except Exception as e:
                print(f'❌ 创建1.{section_num}节时出错: {e}')
                continue
        
        print('🎉 所有节创建完成！')
    
    async def execute_section_copying(self):
        """执行完整的节复制流程"""
        try:
            # 查找1.1节
            page_or_frame, section_element = await self.find_complete_section_11()
            
            if not section_element:
                print('❌ 未找到1.1节，无法继续')
                return False
            
            # 复制1.1节的完整内容
            success = await self.copy_complete_section(page_or_frame, section_element)
            
            if not success:
                print('❌ 复制1.1节失败')
                return False
            
            # 询问用户要创建多少个节
            print('📝 请输入要创建的节数范围：')
            try:
                end_section = int(input('创建到第几节？(例如输入10表示创建1.2到1.10): '))
                if end_section < 2:
                    end_section = 10
            except:
                end_section = 10
                print('使用默认值：创建到1.10节')
            
            # 创建多个节
            await self.create_multiple_sections(page_or_frame, 2, end_section)
            
            # 最终提示
            print('🎉 自动化操作完成！')
            print('📝 请检查并手动调整各节的具体内容')
            print('💡 使用 Ctrl+M 渲染公式')
            print('🔄 如需修改特定节的内容，请手动编辑')
            
            # 截图最终结果
            await self.page.screenshot(path='after_copy_all_sections.png', full_page=True)
            print('📸 已保存最终结果截图: after_copy_all_sections.png')
            
            # 保持浏览器打开
            print('🌐 浏览器保持打开状态，您可以继续编辑...')
            await self.page.wait_for_timeout(300000)  # 等待5分钟
            
            return True
            
        except Exception as e:
            print(f'❌ 执行过程中发生错误: {e}')
            if self.page:
                await self.page.screenshot(path='error_final.png', full_page=True)
            return False

# 运行工具
async def main():
    tool = SectionCopyTool()
    await tool.start_browser_and_wait_for_login()

if __name__ == "__main__":
    asyncio.run(main())
