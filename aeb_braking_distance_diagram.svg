<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4a90e2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357abd;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="reactionGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.85" />
      <stop offset="100%" style="stop-color:#ffa8a8;stop-opacity:0.85" />
    </linearGradient>
    <linearGradient id="brakingGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#51cf66;stop-opacity:0.85" />
      <stop offset="100%" style="stop-color:#8ce99a;stop-opacity:0.85" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="4" flood-color="#00000025"/>
    </filter>
    <style>
      .title { font-family: SimSun, serif; font-size: 32px; font-weight: bold; fill: white; text-anchor: middle; }
      .section-title { font-family: SimSun, serif; font-size: 20px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .text { font-family: SimSun, serif; font-size: 16px; fill: #495057; }
      .formula { font-family: "Consolas", "SimSun", monospace; font-size: 16px; fill: #6f42c1; font-weight: bold; text-anchor: middle; }
      .result { font-family: SimSun, serif; font-size: 16px; fill: #28a745; font-weight: bold; }
      .highlight { font-family: SimSun, serif; font-size: 20px; fill: #dc3545; font-weight: bold; text-anchor: middle; }
      .card { fill: #ffffff; stroke: #dee2e6; stroke-width: 1; filter: url(#shadow); rx: 10; }
      .header-card { fill: url(#headerGrad); stroke: none; filter: url(#shadow); rx: 15; }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#4a90e2" />
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1000" fill="#f8f9fa"/>
  
  <!-- 标题区域 -->
  <rect x="50" y="30" width="1700" height="70" class="header-card" />
  <text x="900" y="75" class="title">AEB系统制动距离计算分析</text>
  
  <!-- 参数卡片 -->
  <rect x="80" y="130" width="320" height="200" class="card" />
  <rect x="90" y="140" width="300" height="35" fill="#667eea" rx="4" />
  <text x="240" y="162" text-anchor="middle" style="font-family: SimSun, serif; font-size: 18px; font-weight: bold; fill: white;">测试参数</text>
  
  <circle cx="110" cy="200" r="4" fill="#667eea" />
  <text x="125" y="205" class="text">测试车速：V₀ = 50 km/h</text>
  <circle cx="110" cy="225" r="4" fill="#667eea" />
  <text x="125" y="230" class="text">反应时间：t = 0.5 s</text>
  <circle cx="110" cy="250" r="4" fill="#667eea" />
  <text x="125" y="255" class="text">减速度：a = 9.8 m/s²</text>
  <circle cx="110" cy="275" r="4" fill="#667eea" />
  <text x="125" y="280" class="text">初始距离：15.0 m</text>
  <text x="240" y="310" text-anchor="middle" class="section-title">目标：计算总制动距离</text>
  
  <!-- 计算流程卡片 -->
  <rect x="450" y="130" width="580" height="200" class="card" />
  <rect x="460" y="140" width="560" height="35" fill="#8e44ad" rx="4" />
  <text x="740" y="162" text-anchor="middle" style="font-family: SimSun, serif; font-size: 18px; font-weight: bold; fill: white;">计算流程</text>
  
  <!-- 步骤框 -->
  <rect x="480" y="190" width="160" height="50" fill="#f8f9fa" stroke="#667eea" stroke-width="2" rx="6" />
  <text x="560" y="210" text-anchor="middle" class="text">单位换算</text>
  <text x="560" y="225" text-anchor="middle" class="formula">13.89 m/s</text>
  
  <rect x="660" y="190" width="160" height="50" fill="#f8f9fa" stroke="#e74c3c" stroke-width="2" rx="6" />
  <text x="740" y="210" text-anchor="middle" class="text">反应距离</text>
  <text x="740" y="225" text-anchor="middle" class="formula">6.945 m</text>
  
  <rect x="840" y="190" width="160" height="50" fill="#f8f9fa" stroke="#27ae60" stroke-width="2" rx="6" />
  <text x="920" y="210" text-anchor="middle" class="text">制动距离</text>
  <text x="920" y="225" text-anchor="middle" class="formula">9.84 m</text>
  
  <!-- 箭头连接 -->
  <path d="M 640 215 L 655 215" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)" />
  <path d="M 820 215 L 835 215" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)" />
  
  <!-- 公式展示 -->
  <text x="560" y="270" text-anchor="middle" class="text">V₀ = 50×(1000/3600)</text>
  <text x="740" y="270" text-anchor="middle" class="text">D₁ = V₀ × t</text>
  <text x="920" y="270" text-anchor="middle" class="text">D₂ = V₀²/(2a)</text>
  
  <!-- 距离可视化区域 -->
  <rect x="80" y="360" width="1340" height="180" class="card" />
  <rect x="90" y="370" width="1320" height="35" fill="#34495e" rx="4" />
  <text x="750" y="392" text-anchor="middle" style="font-family: SimSun, serif; font-size: 18px; font-weight: bold; fill: white;">制动距离可视化分析</text>
  
  <!-- 道路基线 -->
  <rect x="120" y="450" width="1200" height="8" fill="#7f8c8d" rx="4" />
  
  <!-- 车辆起始位置 -->
  <g transform="translate(120, 430)">
    <rect x="0" y="0" width="50" height="25" fill="#2c3e50" rx="3" />
    <rect x="5" y="5" width="15" height="15" fill="#3498db" rx="2" />
    <rect x="30" y="5" width="15" height="15" fill="#3498db" rx="2" />
    <circle cx="10" cy="30" r="5" fill="#34495e" />
    <circle cx="40" cy="30" r="5" fill="#34495e" />
  </g>
  <text x="145" y="415" text-anchor="middle" class="text">车辆</text>
  <text x="145" y="485" text-anchor="middle" class="text">50 km/h</text>
  
  <!-- 反应距离段 -->
  <rect x="170" y="440" width="278" height="28" fill="url(#reactionGrad)" rx="4" />
  <text x="309" y="420" text-anchor="middle" class="section-title">反应距离</text>
  <text x="309" y="460" text-anchor="middle" class="highlight">6.945 m</text>
  <text x="309" y="490" text-anchor="middle" class="text">系统响应阶段</text>
  
  <!-- 制动距离段 -->
  <rect x="448" y="440" width="394" height="28" fill="url(#brakingGrad)" rx="4" />
  <text x="645" y="420" text-anchor="middle" class="section-title">制动距离</text>
  <text x="645" y="460" text-anchor="middle" class="highlight">9.84 m</text>
  <text x="645" y="490" text-anchor="middle" class="text">制动减速阶段</text>
  
  <!-- 总距离标注 -->
  <path d="M 170 510 L 842 510" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead)" />
  <text x="506" y="505" text-anchor="middle" class="section-title">总制动距离</text>
  <text x="506" y="525" text-anchor="middle" class="highlight">16.785 m</text>
  
  <!-- 障碍物 -->
  <g transform="translate(900, 425)">
    <rect x="0" y="0" width="40" height="45" fill="#e74c3c" rx="4" />
    <rect x="5" y="5" width="30" height="8" fill="#c0392b" />
    <rect x="5" y="18" width="30" height="8" fill="#c0392b" />
    <rect x="5" y="31" width="30" height="8" fill="#c0392b" />
  </g>
  <text x="920" y="415" text-anchor="middle" class="text">障碍物</text>
  
  <!-- 可用距离标注 -->
  <path d="M 120 500 L 900 500" stroke="#95a5a6" stroke-width="2" stroke-dasharray="5,5" />
  <text x="510" y="495" text-anchor="middle" class="text">可用距离：15.0 m</text>
  
  <!-- 结论分析卡片 -->
  <rect x="1080" y="130" width="340" height="410" class="card" />
  <rect x="1090" y="140" width="320" height="35" fill="#e74c3c" rx="4" />
  <text x="1250" y="162" text-anchor="middle" style="font-family: SimSun, serif; font-size: 18px; font-weight: bold; fill: white;">安全性分析</text>
  
  <!-- 数据对比 -->
  <rect x="1110" y="200" width="280" height="80" fill="#f8f9fa" stroke="#e1e8ed" stroke-width="1" rx="6" />
  <text x="1250" y="220" text-anchor="middle" class="section-title">距离对比</text>
  <text x="1130" y="245" class="text">所需距离：</text>
  <text x="1350" y="245" text-anchor="end" class="highlight">16.785 m</text>
  <text x="1130" y="265" class="text">可用距离：</text>
  <text x="1350" y="265" text-anchor="end" class="text">15.0 m</text>
  
  <!-- 结果显示 -->
  <rect x="1110" y="300" width="280" height="60" fill="#ffebee" stroke="#e74c3c" stroke-width="2" rx="6" />
  <text x="1250" y="325" text-anchor="middle" class="section-title">16.785 > 15.0</text>
  <text x="1250" y="345" text-anchor="middle" class="highlight">测试不达标</text>
  
  <!-- 风险提示 -->
  <rect x="1110" y="380" width="280" height="80" fill="#fff3cd" stroke="#ffc107" stroke-width="1" rx="6" />
  <text x="1250" y="400" text-anchor="middle" class="section-title">风险评估</text>
  <text x="1130" y="420" class="text">超出距离：1.785 m</text>
  <text x="1130" y="440" class="text">碰撞概率：高</text>
  <text x="1130" y="455" class="text">建议：优化系统参数</text>
  
  <!-- 公式参考卡片 -->
  <rect x="80" y="570" width="1640" height="180" class="card" />
  <rect x="90" y="580" width="1620" height="35" fill="#27ae60" rx="4" />
  <text x="900" y="602" text-anchor="middle" style="font-family: SimSun, serif; font-size: 18px; font-weight: bold; fill: white;">核心公式与原理</text>
  
  <!-- 公式网格 -->
  <g transform="translate(120, 640)">
    <rect x="0" y="0" width="380" height="40" fill="#f8f9fa" stroke="#27ae60" stroke-width="1" rx="4" />
    <text x="10" y="25" class="text">速度换算：V = V₀ × (1000/3600)</text>
    
    <rect x="400" y="0" width="380" height="40" fill="#f8f9fa" stroke="#e74c3c" stroke-width="1" rx="4" />
    <text x="410" y="25" class="text">反应距离：D₁ = V × t</text>
    
    <rect x="800" y="0" width="380" height="40" fill="#f8f9fa" stroke="#8e44ad" stroke-width="1" rx="4" />
    <text x="810" y="25" class="text">制动距离：D₂ = V²/(2a)</text>
    
    <rect x="1200" y="0" width="380" height="40" fill="#f8f9fa" stroke="#667eea" stroke-width="1" rx="4" />
    <text x="1210" y="25" class="text">总距离：D = D₁ + D₂</text>
  </g>
  
  <text x="900" y="720" text-anchor="middle" class="text">基于运动学原理：v² = v₀² + 2as，当 D总 ≤ D场景 时系统可避免碰撞</text>
</svg>