<?xml version="1.0" encoding="UTF-8"?>
<svg width="1500" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="reactionGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff9a9e;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#fecfef;stop-opacity:0.8" />
    </linearGradient>
    <linearGradient id="brakingGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#a8edea;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#fed6e3;stop-opacity:0.8" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#00000020"/>
    </filter>
    <style>
      .title { font-family: SimSun, serif; font-size: 28px; font-weight: bold; fill: white; }
      .section-title { font-family: SimSun, serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: SimSun, serif; font-size: 16px; fill: #34495e; }
      .formula { font-family: SimSun, serif; font-size: 16px; fill: #8e44ad; font-weight: bold; }
      .result { font-family: SimSun, serif; font-size: 16px; fill: #27ae60; font-weight: bold; }
      .highlight { font-family: SimSun, serif; font-size: 18px; fill: #e74c3c; font-weight: bold; }
      .card { fill: white; stroke: #e1e8ed; stroke-width: 1; filter: url(#shadow); rx: 8; }
      .header-card { fill: url(#headerGrad); stroke: none; filter: url(#shadow); rx: 12; }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#667eea" />
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1500" height="800" fill="#f8f9fa"/>
  
  <!-- 标题区域 -->
  <rect x="50" y="30" width="1400" height="70" class="header-card" />
  <text x="750" y="75" text-anchor="middle" class="title">AEB系统制动距离计算分析</text>
  
  <!-- 参数卡片 -->
  <rect x="80" y="130" width="320" height="200" class="card" />
  <rect x="90" y="140" width="300" height="35" fill="#667eea" rx="4" />
  <text x="240" y="162" text-anchor="middle" style="font-family: SimSun, serif; font-size: 18px; font-weight: bold; fill: white;">测试参数</text>
  
  <circle cx="110" cy="200" r="4" fill="#667eea" />
  <text x="125" y="205" class="text">测试车速：V₀ = 50 km/h</text>
  <circle cx="110" cy="225" r="4" fill="#667eea" />
  <text x="125" y="230" class="text">反应时间：t = 0.5 s</text>
  <circle cx="110" cy="250" r="4" fill="#667eea" />
  <text x="125" y="255" class="text">减速度：a = 9.8 m/s²</text>
  <circle cx="110" cy="275" r="4" fill="#667eea" />
  <text x="125" y="280" class="text">初始距离：15.0 m</text>
  <text x="240" y="310" text-anchor="middle" class="section-title">目标：计算总制动距离</text>
  
  <!-- 计算流程卡片 -->
  <rect x="450" y="130" width="580" height="200" class="card" />
  <rect x="460" y="140" width="560" height="35" fill="#8e44ad" rx="4" />
  <text x="740" y="162" text-anchor="middle" style="font-family: SimSun, serif; font-size: 18px; font-weight: bold; fill: white;">计算流程</text>
  
  <!-- 步骤框 -->
  <rect x="480" y="190" width="160" height="50" fill="#f8f9fa" stroke="#667eea" stroke-width="2" rx="6" />
  <text x="560" y="210" text-anchor="middle" class="text">单位换算</text>
  <text x="560" y="225" text-anchor="middle" class="formula">13.89 m/s</text>
  
  <rect x="660" y="190" width="160" height="50" fill="#f8f9fa" stroke="#e74c3c" stroke-width="2" rx="6" />
  <text x="740" y="210" text-anchor="middle" class="text">反应距离</text>
  <text x="740" y="225" text-anchor="middle" class="formula">6.945 m</text>
  
  <rect x="840" y="190" width="160" height="50" fill="#f8f9fa" stroke="#27ae60" stroke-width="2" rx="6" />
  <text x="920" y="210" text-anchor="middle" class="text">制动距离</text>
  <text x="920" y="225" text-anchor="middle" class="formula">9.84 m</text>
  
  <!-- 箭头连接 -->
  <path d="M 640 215 L 655 215" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)" />
  <path d="M 820 215 L 835 215" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)" />
  
  <!-- 公式展示 -->
  <text x="560" y="270" text-anchor="middle" class="text">V₀ = 50×(1000/3600)</text>
  <text x="740" y="270" text-anchor="middle" class="text">D₁ = V₀ × t</text>
  <text x="920" y="270" text-anchor="middle" class="text">D₂ = V₀²/(2a)</text>
  
  <!-- 距离示意图 -->
  <rect x="100" y="300" width="1300" height="200" class="box" />
  <text x="750" y="325" text-anchor="middle" class="section-title">制动距离示意图</text>
  
  <!-- 车辆图标 -->
  <rect x="120" y="360" width="40" height="20" fill="#2c3e50" />
  <text x="140" y="355" text-anchor="middle" class="text">车辆</text>
  <text x="140" y="395" text-anchor="middle" class="text">V₀=50km/h</text>
  
  <!-- 反应距离 -->
  <rect x="180" y="360" width="278" height="20" class="reaction-bar" />
  <text x="319" y="355" text-anchor="middle" class="text">反应距离</text>
  <text x="319" y="385" text-anchor="middle" class="result">6.945 m</text>
  <text x="319" y="405" text-anchor="middle" class="text">（系统反应期间车辆继续前进）</text>
  
  <!-- 制动距离 -->
  <rect x="458" y="360" width="394" height="20" class="braking-bar" />
  <text x="655" y="355" text-anchor="middle" class="text">制动距离</text>
  <text x="655" y="385" text-anchor="middle" class="result">9.84 m</text>
  <text x="655" y="405" text-anchor="middle" class="text">（制动系统作用至完全停止）</text>
  
  <!-- 总距离标注 -->
  <line x1="180" y1="430" x2="852" y2="430" class="arrow" />
  <text x="516" y="450" text-anchor="middle" class="section-title">总制动距离</text>
  <text x="516" y="470" text-anchor="middle" class="result">D总 = 6.945 + 9.84 = 16.785 m</text>
  
  <!-- 障碍物 -->
  <rect x="900" y="350" width="30" height="40" fill="#e74c3c" />
  <text x="915" y="345" text-anchor="middle" class="text">障碍物</text>
  
  <!-- 场景距离标注 -->
  <line x1="120" y1="520" x2="900" y2="520" stroke="#95a5a6" stroke-width="2" />
  <text x="510" y="540" text-anchor="middle" class="text">场景初始距离：15.0 m</text>
  
  <!-- 结论 -->
  <rect x="1100" y="300" width="300" height="200" class="box" />
  <text x="1250" y="325" text-anchor="middle" class="section-title">结论判定</text>
  <text x="1120" y="360" class="text">所需距离：16.785 m</text>
  <text x="1120" y="385" class="text">可用距离：15.0 m</text>
  <text x="1120" y="420" class="text">16.785 > 15.0</text>
  <rect x="1120" y="440" width="260" height="40" fill="#e74c3c" opacity="0.2" stroke="#e74c3c" stroke-width="2" />
  <text x="1250" y="465" text-anchor="middle" class="result" fill="#e74c3c">测试结果：不达标</text>
  
  <!-- 公式说明 -->
  <rect x="50" y="550" width="1400" height="200" class="box" />
  <text x="750" y="575" text-anchor="middle" class="section-title">关键公式说明</text>
  
  <text x="70" y="610" class="text">速度换算公式：V(m/s) = V(km/h) × (1000/3600)</text>
  <text x="70" y="635" class="text">反应距离公式：D反应 = V₀ × t反应</text>
  <text x="70" y="660" class="text">制动距离公式：D制动 = V₀²/(2a)  （基于运动学方程 v² = v₀² + 2as）</text>
  <text x="70" y="685" class="text">总制动距离：D总 = D反应 + D制动</text>
  <text x="70" y="710" class="text">安全判定：当 D总 ≤ D场景 时，系统能够避免碰撞</text>
</svg>