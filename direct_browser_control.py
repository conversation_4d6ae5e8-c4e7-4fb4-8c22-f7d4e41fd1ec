import asyncio
from playwright.async_api import async_playwright

class DirectBrowserControl:
    def __init__(self):
        self.browser = None
        self.context = None
        self.page = None
        
    async def start_browser(self):
        """启动浏览器并导航到目标网站"""
        print('🚀 启动浏览器...')
        
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,
            slow_mo=500
        )
        
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        
        self.page = await self.context.new_page()
        
        print('📱 导航到教材编辑器...')
        await self.page.goto('https://textbookeditor.lessonplan.cn/ed0b4d38-03c8-150b-edab-a4d423944978')
        
        await self.page.wait_for_timeout(3000)
        print('✅ 浏览器已就绪！')
        
    async def login(self):
        """执行登录操作"""
        print('🔐 开始登录...')
        
        # 查找用户名输入框
        username_input = await self.page.wait_for_selector('input[type="text"]', timeout=10000)
        await username_input.fill('13609281701')
        print('✅ 已输入用户名')
        
        # 查找密码输入框
        password_input = await self.page.wait_for_selector('input[type="password"]', timeout=5000)
        await password_input.fill('swy2011040052')
        print('✅ 已输入密码')
        
        # 点击登录按钮（避免点击微信登录）
        login_selectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            'button.login-btn',
            'button.submit-btn',
            '.login-form button',
            'form button[type="submit"]'
        ]

        login_button = None
        for selector in login_selectors:
            try:
                login_button = await self.page.wait_for_selector(selector, timeout=2000)
                if login_button:
                    # 检查按钮文本，确保不是微信登录
                    button_text = await login_button.text_content()
                    if button_text and '微信' not in button_text and '登录' in button_text:
                        print(f'✅ 找到正确的登录按钮: {selector} - {button_text}')
                        break
                    else:
                        login_button = None
            except Exception:
                continue

        if not login_button:
            # 如果找不到，尝试按回车键
            print('🔍 未找到登录按钮，尝试按回车键登录...')
            await password_input.press('Enter')
        else:
            await login_button.click()
            print('✅ 已点击正确的登录按钮')
        
        # 等待登录完成
        await self.page.wait_for_timeout(5000)
        print('✅ 登录完成！')
        
    async def find_section_11(self):
        """查找1.1节内容"""
        print('🔍 查找1.1节内容...')

        # 等待页面加载
        await self.page.wait_for_timeout(5000)

        # 先截图看看页面状态
        await self.page.screenshot(path='current_page.png', full_page=True)
        print('📸 已保存当前页面截图: current_page.png')

        # 尝试多种选择器查找1.1节
        selectors = [
            'text=1.1',
            'h1:has-text("1.1")',
            'h2:has-text("1.1")',
            'h3:has-text("1.1")',
            'h4:has-text("1.1")',
            'div:has-text("1.1")',
            'span:has-text("1.1")',
            'p:has-text("1.1")',
            '*:has-text("1.1")'
        ]

        section_element = None
        print('🔍 在主页面查找1.1节...')
        for selector in selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    print(f'✅ 在主页面找到 {len(elements)} 个匹配元素: {selector}')
                    section_element = elements[0]  # 取第一个
                    # 获取元素文本内容
                    text_content = await section_element.text_content()
                    print(f'📝 元素内容: {text_content[:100]}...')
                    break
            except Exception as e:
                print(f'❌ 选择器 {selector} 出错: {e}')
                continue

        if not section_element:
            # 尝试在iframe中查找
            print('🔍 在iframe中查找1.1节...')
            iframes = await self.page.query_selector_all('iframe')
            print(f'🔍 找到 {len(iframes)} 个iframe')

            for i, iframe in enumerate(iframes):
                try:
                    print(f'🔍 检查第 {i+1} 个iframe...')
                    frame = await iframe.content_frame()
                    if frame:
                        print(f'✅ 成功进入第 {i+1} 个iframe')

                        # 等待iframe内容加载
                        await frame.wait_for_timeout(2000)

                        for selector in selectors:
                            try:
                                elements = await frame.query_selector_all(selector)
                                if elements:
                                    print(f'✅ 在第{i+1}个iframe中找到 {len(elements)} 个匹配元素: {selector}')
                                    section_element = elements[0]
                                    # 获取元素文本内容
                                    text_content = await section_element.text_content()
                                    print(f'📝 iframe中元素内容: {text_content[:100]}...')
                                    return frame, section_element
                            except Exception as e:
                                continue
                    else:
                        print(f'❌ 无法进入第 {i+1} 个iframe')
                except Exception as e:
                    print(f'❌ 处理第 {i+1} 个iframe时出错: {e}')
                    continue

        # 如果还是找不到，尝试查找所有包含数字的元素
        if not section_element:
            print('🔍 尝试查找所有包含数字的元素...')
            try:
                all_elements = await self.page.query_selector_all('*')
                for element in all_elements[:50]:  # 只检查前50个元素
                    try:
                        text = await element.text_content()
                        if text and ('1.1' in text or '1.' in text):
                            print(f'📝 找到可能的元素: {text[:50]}...')
                    except:
                        continue
            except Exception as e:
                print(f'❌ 查找所有元素时出错: {e}')

        return self.page, section_element
    
    async def copy_and_create_section_12(self):
        """复制1.1节并创建1.2节"""
        print('📋 开始复制1.1节并创建1.2节...')
        
        # 查找1.1节
        page_or_frame, section_element = await self.find_section_11()
        
        if not section_element:
            print('❌ 未找到1.1节，无法继续')
            return False
            
        try:
            # 点击1.1节元素
            await section_element.click()
            await page_or_frame.wait_for_timeout(1000)
            
            # 全选并复制
            await page_or_frame.keyboard.press('Control+A')
            await page_or_frame.wait_for_timeout(500)
            await page_or_frame.keyboard.press('Control+C')
            await page_or_frame.wait_for_timeout(500)
            print('✅ 已复制1.1节内容')
            
            # 移动到文档末尾
            await page_or_frame.keyboard.press('Control+End')
            await page_or_frame.keyboard.press('Enter')
            await page_or_frame.keyboard.press('Enter')
            await page_or_frame.wait_for_timeout(500)
            
            # 粘贴内容
            await page_or_frame.keyboard.press('Control+V')
            await page_or_frame.wait_for_timeout(2000)
            print('✅ 已粘贴内容')
            
            # 查找新粘贴的1.1并修改为1.2
            elements_with_11 = await page_or_frame.query_selector_all('*:has-text("1.1")')
            print(f'🔍 找到 {len(elements_with_11)} 个包含1.1的元素')
            
            if len(elements_with_11) > 1:
                # 获取最后一个（新复制的）
                last_element = elements_with_11[-1]
                
                # 获取文本内容
                text_content = await last_element.text_content()
                print(f'📝 最后一个1.1元素内容: {text_content[:50]}...')
                
                # 尝试修改为1.2
                await last_element.click()
                await page_or_frame.wait_for_timeout(500)
                
                # 如果是可编辑的，直接修改
                is_editable = await last_element.is_editable()
                if is_editable:
                    new_content = text_content.replace('1.1', '1.2', 1)  # 只替换第一个
                    await last_element.fill(new_content)
                else:
                    # 尝试双击选择并替换
                    await last_element.dblclick()
                    await page_or_frame.wait_for_timeout(500)
                    await page_or_frame.keyboard.type('1.2')
                
                print('✅ 成功创建1.2节！')
                return True
            else:
                print('❌ 复制可能未成功')
                return False
                
        except Exception as e:
            print(f'❌ 操作过程中出错: {e}')
            return False
    
    async def render_formulas(self):
        """提示用户渲染公式"""
        print('📐 请手动使用 Ctrl+M 渲染公式')
        print('💡 提示：选中公式内容，然后按 Ctrl+M')
        
    async def execute_full_automation(self):
        """执行完整的自动化流程"""
        try:
            await self.start_browser()
            await self.login()
            success = await self.copy_and_create_section_12()
            
            if success:
                await self.render_formulas()
                print('🎉 自动化操作完成！')
                print('📝 请检查并手动调整1.2节的具体内容')
                print('🔄 如需创建更多节，可以重复此过程')
            else:
                print('❌ 自动化操作未完全成功，请手动检查')
                
            # 保持浏览器打开
            print('🌐 浏览器保持打开状态，您可以继续手动操作...')
            await self.page.wait_for_timeout(300000)  # 等待5分钟
            
        except Exception as e:
            print(f'❌ 执行过程中发生错误: {e}')
            if self.page:
                await self.page.screenshot(path='error_screenshot.png', full_page=True)
                print('📸 已保存错误截图')

# 运行自动化
async def main():
    controller = DirectBrowserControl()
    await controller.execute_full_automation()

if __name__ == "__main__":
    asyncio.run(main())
