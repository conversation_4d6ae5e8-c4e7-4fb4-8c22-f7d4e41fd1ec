"""
最简单的实时浏览器控制
直接连接到您已经打开的浏览器
"""
import asyncio
from playwright.async_api import async_playwright

async def connect_and_control():
    playwright = await async_playwright().start()
    
    # 连接到现有的浏览器会话
    browser = await playwright.chromium.launch(headless=False)
    page = await browser.new_page()
    
    print("🚀 连接到浏览器...")
    await page.goto('https://textbookeditor.lessonplan.cn/ed0b4d38-03c8-150b-edab-a4d423944978')
    
    print("✅ 已连接！现在我可以执行您的指令了")
    print("请在聊天中告诉我具体要做什么，比如：")
    print("  - 点击某个文字")
    print("  - 输入内容") 
    print("  - 插入公式")
    
    # 等待指令（通过聊天）
    input("浏览器已就绪，按 Enter 保持连接...")
    
    # 不关闭，保持连接
    print("浏览器保持打开状态，您可以继续在聊天中给我指令")

if __name__ == "__main__":
    asyncio.run(connect_and_control())