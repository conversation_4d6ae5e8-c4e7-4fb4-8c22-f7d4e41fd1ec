import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

# 设置matplotlib参数
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 16
plt.rcParams['mathtext.fontset'] = 'stix'  # 使用STIX字体渲染数学公式
plt.rcParams['mathtext.default'] = 'regular'

try:
    # 创建图形 - 更大的尺寸避免文本挤压
    fig, ax = plt.subplots(1, 1, figsize=(24, 14))  # 增大图形尺寸
    ax.set_xlim(0, 120)  # 增大坐标范围
    ax.set_ylim(0, 120)
    ax.axis('off')

    # 标题
    ax.text(60, 115, 'AEB制动距离计算示意图', fontsize=32, fontweight='bold', 
            ha='center', va='center')

    # 已知参数框 - 增大尺寸和间距
    param_box = FancyBboxPatch((5, 85), 30, 25, boxstyle="round,pad=1", 
                              facecolor='#e3f2fd', edgecolor='#1976d2', linewidth=2)
    ax.add_patch(param_box)
    ax.text(20, 106, '已知参数', fontsize=22, fontweight='bold', ha='center')
    ax.text(8, 102, '测试车速：', fontsize=18, ha='left')
    ax.text(8, 99, r'$V_0 = 50$ km/h', fontsize=18, ha='left')
    ax.text(8, 95, '系统反应时间：', fontsize=18, ha='left')
    ax.text(8, 92, r'$t = 0.5$ s', fontsize=18, ha='left')
    ax.text(8, 88, '最大减速度：', fontsize=18, ha='left')
    ax.text(8, 85, r'$a = 9.8$ m/s²', fontsize=18, ha='left')

    # 计算步骤框 - 增大尺寸
    calc_box = FancyBboxPatch((40, 85), 55, 25, boxstyle="round,pad=1", 
                             facecolor='#fff3e0', edgecolor='#f57c00', linewidth=2)
    ax.add_patch(calc_box)
    ax.text(67.5, 106, '计算步骤', fontsize=22, fontweight='bold', ha='center')

    # 步骤内容 - 增加行间距
    ax.text(42, 102, '步骤一：单位换算', fontsize=18, fontweight='bold', ha='left')
    ax.text(44, 99, r'$V_0 = 50 \times \frac{1000}{3600} = 13.89$ m/s', fontsize=16, ha='left', color='#d32f2f')

    ax.text(42, 95, '步骤二：计算反应距离', fontsize=18, fontweight='bold', ha='left')
    ax.text(44, 92, r'$D_{\mathrm{反应}} = V_0 \times t = 13.89 \times 0.5 = 6.945$ m', fontsize=16, ha='left', color='#d32f2f')

    ax.text(42, 88, '步骤三：计算纯制动距离', fontsize=18, fontweight='bold', ha='left')
    ax.text(44, 85, r'$D_{\mathrm{制动}} = \frac{V_0^2}{2a} = \frac{13.89^2}{2 \times 9.8} = 9.84$ m', fontsize=16, ha='left', color='#d32f2f')

    # 关键公式框
    formula_box = FancyBboxPatch((100, 85), 15, 25, boxstyle="round,pad=1", 
                                facecolor='#e8f5e8', edgecolor='#388e3c', linewidth=2)
    ax.add_patch(formula_box)
    ax.text(107.5, 106, '关键公式', fontsize=22, fontweight='bold', ha='center')
    ax.text(102, 102, '单位换算：', fontsize=18, fontweight='bold', ha='left')
    ax.text(102, 99, r'$V_{(\mathrm{m/s})} = V_{(\mathrm{km/h})} \times \frac{1000}{3600}$', fontsize=14, ha='left')
    ax.text(102, 95, '反应距离：', fontsize=18, fontweight='bold', ha='left')
    ax.text(102, 92, r'$D_{\mathrm{反应}} = V_0 \times t_{\mathrm{反应}}$', fontsize=14, ha='left')
    ax.text(102, 88, '制动距离：', fontsize=18, fontweight='bold', ha='left')
    ax.text(102, 85, r'$D_{\mathrm{制动}} = \frac{V_0^2}{2a}$', fontsize=14, ha='left')

    # 步骤四和结论 - 单独放置
    step4_box = FancyBboxPatch((40, 75), 55, 8, boxstyle="round,pad=1", 
                              facecolor='#fff3e0', edgecolor='#f57c00', linewidth=2)
    ax.add_patch(step4_box)
    ax.text(42, 81, '步骤四：计算总制动距离', fontsize=18, fontweight='bold', ha='left')
    ax.text(44, 78, r'$D_{\mathrm{总}} = D_{\mathrm{反应}} + D_{\mathrm{制动}} = 6.945 + 9.84 = 16.785$ m', fontsize=16, ha='left', color='#d32f2f')

    # 结论框
    conclusion_box = FancyBboxPatch((40, 65), 55, 8, boxstyle="round,pad=1", 
                                   facecolor='#ffebee', edgecolor='#d32f2f', linewidth=3)
    ax.add_patch(conclusion_box)
    ax.text(67.5, 69, r'结论：$16.785$ m $> 15.0$ m，测试不达标！', fontsize=20, ha='center', 
            color='#d32f2f', fontweight='bold')

    # 制动过程示意图
    ax.text(60, 58, '制动过程示意图', fontsize=24, fontweight='bold', ha='center')

    # 地面线
    ax.plot([10, 110], [45, 45], 'k-', linewidth=5)

    # 车辆
    car = patches.Rectangle((12, 46), 8, 4, linewidth=2, edgecolor='#1976d2', facecolor='#2196f3')
    ax.add_patch(car)
    wheel1 = patches.Circle((14, 45.5), 1, linewidth=1, edgecolor='#1976d2', facecolor='#1976d2')
    wheel2 = patches.Circle((18, 45.5), 1, linewidth=1, edgecolor='#1976d2', facecolor='#1976d2')
    ax.add_patch(wheel1)
    ax.add_patch(wheel2)
    ax.text(16, 48, '车辆', fontsize=16, ha='center', color='white', fontweight='bold')

    # 障碍物
    obstacle = patches.Rectangle((95, 46), 6, 5, linewidth=2, edgecolor='#d32f2f', facecolor='#f44336')
    ax.add_patch(obstacle)
    ax.text(98, 48.5, '障碍物', fontsize=16, ha='center', color='white', fontweight='bold')

    # 速度标注
    speed_box = FancyBboxPatch((12, 52), 8, 4, boxstyle="round,pad=0.3", 
                              facecolor='#fff59d', edgecolor='#fbc02d', linewidth=2)
    ax.add_patch(speed_box)
    ax.text(16, 54.5, r'$V_0=50$ km/h', fontsize=14, ha='center', fontweight='bold')
    ax.text(16, 53, r'$(13.89$ m/s$)$', fontsize=12, ha='center')

    # 距离标注 - 增大间距
    # 反应距离
    ax.annotate('', xy=(35, 55), xytext=(20, 55), 
                arrowprops=dict(arrowstyle='<->', color='#ff9800', lw=4))
    ax.text(27.5, 57.5, '反应距离', fontsize=16, ha='center', color='#4caf50', fontweight='bold')
    ax.text(27.5, 55.5, '6.945 m', fontsize=16, ha='center', color='#4caf50', fontweight='bold')

    # 制动距离
    ax.annotate('', xy=(65, 55), xytext=(35, 55), 
                arrowprops=dict(arrowstyle='<->', color='#ff9800', lw=4))
    ax.text(50, 57.5, '制动距离', fontsize=16, ha='center', color='#4caf50', fontweight='bold')
    ax.text(50, 55.5, '9.84 m', fontsize=16, ha='center', color='#4caf50', fontweight='bold')

    # 总距离
    ax.annotate('', xy=(65, 59), xytext=(20, 59), 
                arrowprops=dict(arrowstyle='<->', color='#d32f2f', lw=5))
    ax.text(42.5, 61.5, '总制动距离 16.785 m', fontsize=18, ha='center', color='#d32f2f', fontweight='bold')

    # 场景距离
    ax.plot([20, 95], [42, 42], '#4caf50', linewidth=4, linestyle='--')
    ax.text(57.5, 39, '场景初始距离 15.0 m', fontsize=16, ha='center', color='#4caf50', fontweight='bold')

    # 时间轴
    ax.plot([10, 110], [30, 30], '#666', linewidth=3)
    ax.plot([20, 20], [29, 31], 'k-', linewidth=3)
    ax.plot([35, 35], [29, 31], 'k-', linewidth=3)
    ax.plot([65, 65], [29, 31], 'k-', linewidth=3)

    ax.text(20, 26, 't=0', fontsize=16, ha='center', fontweight='bold')
    ax.text(20, 23, '发现危险', fontsize=14, ha='center')
    ax.text(35, 26, 't=0.5s', fontsize=16, ha='center', fontweight='bold')
    ax.text(35, 23, '开始制动', fontsize=14, ha='center')
    ax.text(65, 26, '完全停止', fontsize=16, ha='center', fontweight='bold')

    # 阶段标注
    reaction_phase = patches.Rectangle((20, 18), 15, 4, linewidth=2, 
                                     edgecolor='#ff9800', facecolor='#ff9800', alpha=0.3)
    ax.add_patch(reaction_phase)
    ax.text(27.5, 20, '反应阶段', fontsize=16, ha='center', fontweight='bold')

    braking_phase = patches.Rectangle((35, 18), 30, 4, linewidth=2, 
                                    edgecolor='#f44336', facecolor='#f44336', alpha=0.3)
    ax.add_patch(braking_phase)
    ax.text(50, 20, '制动阶段', fontsize=16, ha='center', fontweight='bold')

    # 保存为高质量图片
    plt.tight_layout()
    plt.savefig('AEB制动距离计算示意图_专业版.png', dpi=150, bbox_inches='tight', 
                facecolor='white', edgecolor='none', format='png')
    plt.savefig('AEB制动距离计算示意图_专业版.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none', format='pdf')
    plt.savefig('AEB制动距离计算示意图_专业版.svg', bbox_inches='tight', 
                facecolor='white', edgecolor='none', format='svg')
    plt.close()

    print("专业图表已生成完成！")
    print("已保存为PNG、PDF和SVG三种格式")
    print("- PNG格式：适合网页显示和打印")
    print("- PDF格式：适合高质量打印和文档嵌入")
    print("- SVG格式：矢量图，可无限缩放")
    
except Exception as e:
    print(f"生成图表时出错: {e}")
    import traceback
    traceback.print_exc()
