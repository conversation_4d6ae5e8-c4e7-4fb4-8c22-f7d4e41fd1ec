import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 16

try:
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(20, 12))
    ax.set_xlim(0, 100)
    ax.set_ylim(0, 100)
    ax.axis('off')

    # 标题
    ax.text(50, 95, 'AEB制动距离计算示意图', fontsize=28, fontweight='bold', 
            ha='center', va='center')

    # 已知参数框
    param_box = FancyBboxPatch((5, 75), 25, 18, boxstyle="round,pad=0.5", 
                              facecolor='lightblue', edgecolor='navy', linewidth=2)
    ax.add_patch(param_box)
    ax.text(17.5, 90, '已知参数', fontsize=20, fontweight='bold', ha='center')
    ax.text(7, 87, '测试车速 V₀ = 50 km/h', fontsize=16, ha='left')
    ax.text(7, 84, '系统反应时间 t = 0.5 s', fontsize=16, ha='left')
    ax.text(7, 81, '最大减速度 a = 9.8 m/s²', fontsize=16, ha='left')
    ax.text(7, 78, '场景初始距离 = 15.0 m', fontsize=16, ha='left')

    # 计算步骤框
    calc_box = FancyBboxPatch((35, 75), 45, 18, boxstyle="round,pad=0.5", 
                             facecolor='lightyellow', edgecolor='orange', linewidth=2)
    ax.add_patch(calc_box)
    ax.text(57.5, 90, '计算步骤', fontsize=20, fontweight='bold', ha='center')

    # 步骤内容
    ax.text(37, 87, '步骤一：单位换算', fontsize=16, fontweight='bold', ha='left')
    ax.text(39, 85.5, 'V₀ = 50 × (1000/3600) = 13.89 m/s', fontsize=14, ha='left', color='red')

    ax.text(37, 83, '步骤二：计算反应距离', fontsize=16, fontweight='bold', ha='left')
    ax.text(39, 81.5, 'D反应 = V₀ × t = 13.89 × 0.5 = 6.945 m', fontsize=14, ha='left', color='red')

    ax.text(37, 79, '步骤三：计算纯制动距离', fontsize=16, fontweight='bold', ha='left')
    ax.text(39, 77.5, 'D制动 = V₀²/(2a) = 13.89²/(2×9.8) = 9.84 m', fontsize=14, ha='left', color='red')

    ax.text(37, 75.5, '步骤四：计算总制动距离', fontsize=16, fontweight='bold', ha='left')
    ax.text(39, 74, 'D总 = D反应 + D制动 = 6.945 + 9.84 = 16.785 m', fontsize=14, ha='left', color='red')

    # 结论
    ax.text(50, 68, '结论：16.785 m > 15.0 m，测试不达标！', fontsize=18, ha='center', 
            color='red', fontweight='bold', 
            bbox=dict(boxstyle="round,pad=0.5", facecolor='yellow', alpha=0.8))

    # 制动过程示意图
    ax.text(50, 60, '制动过程示意图', fontsize=20, fontweight='bold', ha='center')

    # 地面线
    ax.plot([10, 90], [45, 45], 'k-', linewidth=4)

    # 车辆
    car = patches.Rectangle((12, 46), 6, 3, linewidth=2, edgecolor='blue', facecolor='lightblue')
    ax.add_patch(car)
    ax.text(15, 47.5, '车辆', fontsize=14, ha='center', color='blue', fontweight='bold')

    # 障碍物
    obstacle = patches.Rectangle((75, 46), 4, 4, linewidth=2, edgecolor='red', facecolor='lightcoral')
    ax.add_patch(obstacle)
    ax.text(77, 48, '障碍物', fontsize=14, ha='center', color='white', fontweight='bold')

    # 距离标注
    # 反应距离
    ax.annotate('', xy=(30, 52), xytext=(18, 52), 
                arrowprops=dict(arrowstyle='<->', color='orange', lw=3))
    ax.text(24, 54, '反应距离\n6.945 m', fontsize=14, ha='center', color='green', fontweight='bold')

    # 制动距离
    ax.annotate('', xy=(50, 52), xytext=(30, 52), 
                arrowprops=dict(arrowstyle='<->', color='orange', lw=3))
    ax.text(40, 54, '制动距离\n9.84 m', fontsize=14, ha='center', color='green', fontweight='bold')

    # 总距离
    ax.annotate('', xy=(50, 56), xytext=(18, 56), 
                arrowprops=dict(arrowstyle='<->', color='red', lw=4))
    ax.text(34, 58, '总制动距离 16.785 m', fontsize=16, ha='center', color='red', fontweight='bold')

    # 场景距离
    ax.plot([18, 75], [42, 42], 'g--', linewidth=3)
    ax.text(46.5, 40, '场景初始距离 15.0 m', fontsize=14, ha='center', color='green')

    # 时间轴
    ax.plot([10, 90], [30, 30], 'gray', linewidth=3)
    ax.plot([18, 18], [29, 31], 'k-', linewidth=2)
    ax.plot([30, 30], [29, 31], 'k-', linewidth=2)
    ax.plot([50, 50], [29, 31], 'k-', linewidth=2)

    ax.text(18, 26, 't=0\n发现危险', fontsize=12, ha='center')
    ax.text(30, 26, 't=0.5s\n开始制动', fontsize=12, ha='center')
    ax.text(50, 26, '完全停止', fontsize=12, ha='center')

    # 阶段标注
    reaction_phase = patches.Rectangle((18, 32), 12, 3, linewidth=1, 
                                     edgecolor='orange', facecolor='orange', alpha=0.3)
    ax.add_patch(reaction_phase)
    ax.text(24, 33.5, '反应阶段', fontsize=12, ha='center', fontweight='bold')

    braking_phase = patches.Rectangle((30, 32), 20, 3, linewidth=1, 
                                    edgecolor='red', facecolor='red', alpha=0.3)
    ax.add_patch(braking_phase)
    ax.text(40, 33.5, '制动阶段', fontsize=12, ha='center', fontweight='bold')

    # 关键公式框
    formula_box = FancyBboxPatch((5, 5), 90, 15, boxstyle="round,pad=0.5", 
                                facecolor='lightgreen', edgecolor='green', linewidth=2)
    ax.add_patch(formula_box)
    ax.text(50, 17, '关键公式', fontsize=18, fontweight='bold', ha='center')
    
    ax.text(15, 13, '单位换算：V(m/s) = V(km/h) × 1000/3600', fontsize=14, ha='left')
    ax.text(15, 10, '反应距离：D反应 = V₀ × t反应', fontsize=14, ha='left')
    ax.text(15, 7, '制动距离：D制动 = V₀²/(2a)', fontsize=14, ha='left')

    # 保存图片
    plt.tight_layout()
    plt.savefig('AEB制动距离计算示意图.png', dpi=100, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()

    print("图表已生成完成！")
    print("已保存为 AEB制动距离计算示意图.png")
    
except Exception as e:
    print(f"生成图表时出错: {e}")
    import traceback
    traceback.print_exc()
