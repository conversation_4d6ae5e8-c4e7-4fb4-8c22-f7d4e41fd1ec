import asyncio
from playwright.async_api import async_playwright

class FormatApplier:
    def __init__(self):
        self.browser = None
        self.context = None
        self.page = None
        
    async def start_and_connect(self):
        """启动浏览器并连接到现有页面"""
        print('🚀 启动浏览器并连接...')
        
        async with async_playwright() as p:
            self.browser = await p.chromium.launch(
                headless=False, 
                slow_mo=800,
                args=['--start-maximized']
            )
            
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            
            self.page = await self.context.new_page()
            
            # 导航到页面
            await self.page.goto('https://textbookeditor.lessonplan.cn/ed0b4d38-03c8-150b-edab-a4d423944978')
            await self.page.wait_for_timeout(3000)
            
            # 自动登录
            await self.auto_login()
            
            # 等待页面加载
            await self.page.wait_for_timeout(5000)
            
            # 分析和应用格式
            await self.analyze_and_apply_format()
    
    async def auto_login(self):
        """自动登录"""
        print('🔐 自动登录...')
        
        try:
            # 输入用户名
            username_input = await self.page.wait_for_selector('input[type="text"]', timeout=5000)
            if username_input:
                await username_input.fill('13609281701')
                print('✅ 已输入用户名')
            
            # 输入密码
            password_input = await self.page.wait_for_selector('input[type="password"]', timeout=5000)
            if password_input:
                await password_input.fill('swy2011040052')
                print('✅ 已输入密码')
                
                # 按回车登录
                await password_input.press('Enter')
                print('✅ 已提交登录')
            
            await self.page.wait_for_timeout(5000)
            
        except Exception as e:
            print(f'❌ 登录出错: {e}')
    
    async def find_content_in_editor(self):
        """在编辑器中查找内容"""
        print('🔍 查找编辑器内容...')

        # 等待页面完全加载
        await self.page.wait_for_timeout(5000)

        # 查找iframe编辑器
        iframes = await self.page.query_selector_all('iframe')
        print(f'🔍 找到 {len(iframes)} 个iframe')

        for i, iframe in enumerate(iframes):
            try:
                frame = await iframe.content_frame()
                if frame:
                    print(f'🔍 检查第 {i+1} 个iframe...')

                    # 等待内容加载
                    await frame.wait_for_timeout(2000)

                    # 获取所有文本内容
                    content = await frame.content()
                    if '1.1' in content or '函数' in content:
                        print(f'✅ 第 {i+1} 个iframe包含目标内容')
                        return frame
            except Exception as e:
                print(f'❌ 检查第 {i+1} 个iframe出错: {e}')
                continue

        # 如果iframe中没找到，检查主页面
        print('🔍 在主页面查找内容...')
        main_content = await self.page.content()
        if '1.1' in main_content or '函数' in main_content:
            print('✅ 主页面包含目标内容')
            return self.page

        # 尝试点击进入编辑模式
        print('🔍 尝试进入编辑模式...')
        try:
            # 查找可能的编辑按钮
            edit_selectors = [
                'button:has-text("编辑")',
                'button:has-text("Edit")',
                '.edit-btn',
                '[data-action="edit"]',
                '.editor-trigger'
            ]

            for selector in edit_selectors:
                try:
                    edit_btn = await self.page.wait_for_selector(selector, timeout=2000)
                    if edit_btn:
                        await edit_btn.click()
                        print(f'✅ 点击了编辑按钮: {selector}')
                        await self.page.wait_for_timeout(3000)

                        # 重新查找iframe
                        new_iframes = await self.page.query_selector_all('iframe')
                        if len(new_iframes) > len(iframes):
                            print('✅ 编辑模式下找到新的iframe')
                            for iframe in new_iframes[len(iframes):]:
                                frame = await iframe.content_frame()
                                if frame:
                                    return frame
                        break
                except:
                    continue
        except Exception as e:
            print(f'❌ 进入编辑模式失败: {e}')

        return self.page
    
    async def analyze_and_apply_format(self):
        """分析1.1节格式并应用到其他内容"""
        print('📋 开始分析和应用格式...')
        
        # 找到编辑器
        editor = await self.find_content_in_editor()
        
        # 截图当前状态
        await self.page.screenshot(path='before_format.png', full_page=True)
        print('📸 已保存格式化前截图')
        
        # 查找1.1节元素
        section_11_element = await self.find_section_11(editor)
        
        if not section_11_element:
            print('❌ 未找到1.1节，无法获取格式')
            return
        
        # 分析1.1节的格式
        format_info = await self.analyze_section_format(editor, section_11_element)
        print(f'📊 1.1节格式信息: {format_info}')
        
        # 应用格式到其他内容
        await self.apply_format_to_content(editor, format_info)
        
        # 最终截图
        await self.page.screenshot(path='after_format.png', full_page=True)
        print('📸 已保存格式化后截图')
        
        print('🎉 格式应用完成！')
        
        # 保持浏览器打开
        print('🌐 浏览器保持打开状态...')
        await asyncio.sleep(300)
    
    async def find_section_11(self, editor):
        """查找1.1节元素"""
        print('🔍 查找1.1节...')
        
        selectors = [
            'h1:has-text("1.1")',
            'h2:has-text("1.1")',
            'h3:has-text("1.1")',
            'h4:has-text("1.1")',
            'p:has-text("1.1")',
            'div:has-text("1.1")',
            '*:has-text("1.1")'
        ]
        
        for selector in selectors:
            try:
                elements = await editor.query_selector_all(selector)
                for element in elements:
                    text = await element.text_content()
                    if text and '1.1' in text and '函数' in text:
                        print(f'✅ 找到1.1节: {text[:50]}...')
                        return element
            except Exception:
                continue
        
        return None
    
    async def analyze_section_format(self, editor, section_element):
        """分析1.1节的格式"""
        print('📊 分析1.1节格式...')
        
        try:
            # 获取元素的样式信息
            format_info = await section_element.evaluate('''
                element => {
                    const styles = window.getComputedStyle(element);
                    return {
                        tagName: element.tagName,
                        fontSize: styles.fontSize,
                        fontWeight: styles.fontWeight,
                        fontFamily: styles.fontFamily,
                        color: styles.color,
                        textAlign: styles.textAlign,
                        marginTop: styles.marginTop,
                        marginBottom: styles.marginBottom,
                        paddingTop: styles.paddingTop,
                        paddingBottom: styles.paddingBottom,
                        lineHeight: styles.lineHeight,
                        className: element.className,
                        innerHTML: element.innerHTML
                    };
                }
            ''')
            
            return format_info
            
        except Exception as e:
            print(f'❌ 分析格式出错: {e}')
            return None
    
    async def apply_format_to_content(self, editor, format_info):
        """应用格式到其他内容"""
        print('🎨 应用格式到其他内容...')
        
        if not format_info:
            return
        
        # 定义要格式化的内容列表
        sections_to_format = [
            ('1.2', '数形结合思想'),
            ('1.3', '分类讨论思想'),
            ('1.4', '转化与化归思想'),
            ('1.5', '特殊与一般思想'),
            ('1.6', '有限与无限思想'),
            ('1.7', '或然与必然思想'),
            ('1.8', '算法思想'),
            ('1.9', '模型思想'),
            ('1.10', '应用意识')
        ]
        
        for section_num, section_title in sections_to_format:
            await self.format_single_section(editor, section_num, section_title, format_info)
    
    async def format_single_section(self, editor, section_num, section_title, format_info):
        """格式化单个节"""
        print(f'🎨 格式化{section_num}节: {section_title}')
        
        try:
            # 查找包含节号的文本
            elements = await editor.query_selector_all('*')
            
            for element in elements:
                try:
                    text = await element.text_content()
                    if text and section_num in text and len(text.strip()) < 100:  # 找到标题行
                        print(f'✅ 找到{section_num}节元素')
                        
                        # 应用1.1节的格式
                        await element.evaluate(f'''
                            element => {{
                                // 应用样式
                                element.style.fontSize = "{format_info['fontSize']}";
                                element.style.fontWeight = "{format_info['fontWeight']}";
                                element.style.fontFamily = "{format_info['fontFamily']}";
                                element.style.color = "{format_info['color']}";
                                element.style.textAlign = "{format_info['textAlign']}";
                                element.style.marginTop = "{format_info['marginTop']}";
                                element.style.marginBottom = "{format_info['marginBottom']}";
                                element.style.paddingTop = "{format_info['paddingTop']}";
                                element.style.paddingBottom = "{format_info['paddingBottom']}";
                                element.style.lineHeight = "{format_info['lineHeight']}";
                                
                                // 如果有类名，也应用
                                if ("{format_info['className']}") {{
                                    element.className = "{format_info['className']}";
                                }}
                                
                                // 设置标签类型
                                if (element.tagName !== "{format_info['tagName']}") {{
                                    const newElement = document.createElement("{format_info['tagName']}");
                                    newElement.innerHTML = element.innerHTML;
                                    newElement.className = element.className;
                                    // 复制所有样式
                                    for (let style in element.style) {{
                                        if (element.style[style]) {{
                                            newElement.style[style] = element.style[style];
                                        }}
                                    }}
                                    element.parentNode.replaceChild(newElement, element);
                                }}
                            }}
                        ''')
                        
                        print(f'✅ 已应用格式到{section_num}节')
                        break
                        
                except Exception:
                    continue
                    
        except Exception as e:
            print(f'❌ 格式化{section_num}节出错: {e}')
    
    async def create_formatted_content(self, editor):
        """创建格式化的内容"""
        print('📝 创建格式化内容...')
        
        # 移动到文档末尾
        await editor.keyboard.press('Control+End')
        await editor.keyboard.press('Enter')
        await editor.keyboard.press('Enter')
        
        # 创建内容模板
        content_template = '''
1.2   数形结合思想
数形结合思想，即通过数与形的相互转化来解决数学问题。它包括以形助数和以数解形两个方面。

1.3   分类讨论思想  
分类讨论思想，即当问题的对象不能进行统一研究时，需要对研究对象按某个标准进行分类，然后对每一类分别研究得出结论。

1.4   转化与化归思想
转化与化归思想，即把待解决的问题转化为已经解决或比较容易解决的问题。

1.5   特殊与一般思想
特殊与一般思想，即通过特殊情况的研究，发现一般规律；或者通过一般规律，解决特殊问题。

1.6   有限与无限思想
有限与无限思想，即用有限的方法研究无限的问题，或者用无限的观点处理有限的问题。

1.7   或然与必然思想
或然与必然思想，即研究随机现象中的必然规律，或者在必然中寻找偶然因素。

1.8   算法思想
算法思想，即用算法的观点分析问题，设计解决问题的步骤和方法。

1.9   模型思想
模型思想，即建立数学模型来解决实际问题的思想方法。

1.10  应用意识
应用意识，即运用数学知识解决实际问题的意识和能力。
'''
        
        # 输入内容
        await editor.keyboard.type(content_template)
        await editor.wait_for_timeout(2000)
        
        print('✅ 已创建格式化内容')

# 运行格式应用工具
async def main():
    applier = FormatApplier()
    await applier.start_and_connect()

if __name__ == "__main__":
    asyncio.run(main())
