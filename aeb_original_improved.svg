<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1800" height="1000" viewBox="0 0 1800 1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#333" />
    </marker>
    <style>
      .title { 
        font-family: "Microsoft YaHei", "SimSun", serif; 
        font-size: 36px; 
        font-weight: bold; 
        text-anchor: middle; 
        fill: #2c3e50; 
      }
      .subtitle { 
        font-family: "Microsoft YaHei", "SimSun", serif; 
        font-size: 24px; 
        font-weight: normal; 
        text-anchor: middle; 
        fill: #34495e; 
      }
      .label { 
        font-family: "SimSun", serif; 
        font-size: 16px; 
        text-anchor: middle; 
        fill: #2c3e50; 
      }
      .formula { 
        font-family: "Times New Roman", "SimSun", serif; 
        font-size: 16px; 
        text-anchor: middle; 
        fill: #8e44ad; 
        font-weight: bold;
      }
      .result { 
        font-family: "SimSun", serif; 
        font-size: 20px; 
        font-weight: bold; 
        text-anchor: middle; 
      }
      .bg { 
        fill: #ffffff; 
      }
      .timeline { 
        stroke: #2c3e50; 
        stroke-width: 3; 
        marker-end: url(#arrowhead); 
      }
      .reaction-box { 
        fill: #ff7675; 
        fill-opacity: 0.8; 
      }
      .braking-box { 
        fill: #74b9ff; 
        fill-opacity: 0.8; 
      }
      .scenario-box { 
        fill: #f8f9fa; 
        stroke: #dee2e6; 
        stroke-width: 2;
      }
      .conclusion-box { 
        fill: #ffebee; 
        stroke: #e74c3c; 
        stroke-width: 2;
      }
      .conclusion-text { 
        fill: #c0392b; 
      }
    </style>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" class="bg"/>

  <!-- 标题 -->
  <text x="900" y="60" class="title">AEB系统性能极限验证</text>
  <text x="900" y="100" class="subtitle">制动距离计算示意图</text>

  <!-- 测试场景设定 -->
  <rect x="80" y="150" width="420" height="180" rx="12" class="scenario-box"/>
  <text x="290" y="185" class="label" style="font-weight: bold; font-size: 20px;">1. 测试场景设定</text>
  <text x="290" y="220" class="label">车辆初始速度 (V₀): 50 km/h</text>
  <text x="290" y="250" class="label">系统反应时间 (t): 0.5 s</text>
  <text x="290" y="280" class="label">最大减速度 (a): 9.8 m/s²</text>
  <text x="290" y="310" class="label">场景初始距离: 15.0 m</text>

  <!-- 计算步骤 -->
  <text x="900" y="185" class="label" style="font-weight: bold; font-size: 20px;">2. 制动距离计算分解</text>

  <!-- 步骤1：单位换算 -->
  <rect x="580" y="210" width="200" height="80" rx="8" fill="#e8f4fd" stroke="#3498db" stroke-width="2"/>
  <text x="680" y="235" text-anchor="middle" class="label" style="font-weight: bold;">步骤1：单位换算</text>
  <text x="680" y="255" text-anchor="middle" class="formula">V = 50 × (1000/3600)</text>
  <text x="680" y="275" text-anchor="middle" class="result" fill="#2980b9">= 13.89 m/s</text>

  <!-- 步骤2：反应距离 -->
  <rect x="800" y="210" width="200" height="80" rx="8" fill="#ffeaa7" stroke="#fdcb6e" stroke-width="2"/>
  <text x="900" y="235" text-anchor="middle" class="label" style="font-weight: bold;">步骤2：反应距离</text>
  <text x="900" y="255" text-anchor="middle" class="formula">D₁ = V × t</text>
  <text x="900" y="275" text-anchor="middle" class="result" fill="#e17055">= 6.945 m</text>

  <!-- 步骤3：制动距离 -->
  <rect x="1020" y="210" width="200" height="80" rx="8" fill="#d1f2eb" stroke="#00b894" stroke-width="2"/>
  <text x="1120" y="235" text-anchor="middle" class="label" style="font-weight: bold;">步骤3：制动距离</text>
  <text x="1120" y="255" text-anchor="middle" class="formula">D₂ = V²/(2a)</text>
  <text x="1120" y="275" text-anchor="middle" class="result" fill="#00b894">= 9.84 m</text>

  <!-- 箭头连接 -->
  <path d="M 780 250 L 795 250" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead)" />
  <path d="M 1000 250 L 1015 250" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead)" />

  <!-- 距离示意图 -->
  <text x="900" y="380" class="label" style="font-weight: bold; font-size: 20px;">3. 制动距离可视化</text>

  <!-- 时间轴 -->
  <line x1="150" y1="450" x2="1650" y2="450" class="timeline" />
  <text x="150" y="475" class="label">0 m</text>

  <!-- 车辆图标 -->
  <rect x="120" y="420" width="60" height="30" fill="#2c3e50" rx="5" />
  <text x="150" y="410" text-anchor="middle" class="label">测试车辆</text>
  <text x="150" y="500" text-anchor="middle" class="label">V₀ = 50 km/h</text>

  <!-- 反应距离 -->
  <rect x="180" y="430" width="417" height="40" class="reaction-box" rx="6" />
  <text x="388" y="405" class="result" fill="#e17055">反应距离: 6.945 m</text>
  <text x="388" y="485" class="label">系统反应期间车辆继续前进</text>
  <text x="388" y="505" class="formula">13.89 m/s × 0.5 s = 6.945 m</text>

  <!-- 分界线 -->
  <line x1="597" y1="420" x2="597" y2="480" stroke="#2c3e50" stroke-dasharray="4,4" stroke-width="2"/>
  <text x="597" y="495" text-anchor="middle" class="label">6.945 m</text>

  <!-- 制动距离 -->
  <rect x="597" y="430" width="590" height="40" class="braking-box" rx="6" />
  <text x="892" y="405" class="result" fill="#0984e3">制动距离: 9.84 m</text>
  <text x="892" y="485" class="label">制动系统作用至完全停止</text>
  <text x="892" y="505" class="formula">(13.89 m/s)² / (2 × 9.8 m/s²) = 9.84 m</text>

  <!-- 总距离标注 -->
  <line x1="1187" y1="420" x2="1187" y2="480" stroke="#2c3e50" stroke-dasharray="4,4" stroke-width="2"/>
  <text x="1187" y="495" text-anchor="middle" class="label">16.785 m</text>

  <!-- 总距离箭头 -->
  <path d="M 180 540 L 1187 540" stroke="#8e44ad" stroke-width="4" marker-end="url(#arrowhead)" />
  <text x="683" y="530" text-anchor="middle" class="label" style="font-weight: bold; font-size: 18px;">总制动距离</text>
  <text x="683" y="560" text-anchor="middle" class="result" fill="#8e44ad" style="font-size: 18px;">D总 = 6.945 + 9.84 = 16.785 m</text>

  <!-- 障碍物 -->
  <rect x="1350" y="410" width="50" height="60" fill="#e74c3c" rx="6" />
  <text x="1375" y="400" text-anchor="middle" class="label">静止障碍物</text>

  <!-- 场景距离标注 -->
  <path d="M 120 580 L 1350 580" stroke="#95a5a6" stroke-width="3" stroke-dasharray="8,8" />
  <text x="735" y="570" text-anchor="middle" class="label">测试场景可用距离：15.0 m</text>

  <!-- 结论判定 -->
  <text x="900" y="640" class="label" style="font-weight: bold; font-size: 20px;">4. 结论判定</text>

  <text x="450" y="680" class="label">总制动距离 = 反应距离 + 制动距离</text>
  <text x="450" y="710" class="formula" style="font-size: 18px;">6.945 m + 9.84 m = 16.785 m</text>

  <rect x="800" y="660" width="600" height="120" rx="12" class="conclusion-box"/>
  <text x="1100" y="700" text-anchor="middle" class="result conclusion-text" style="font-size: 28px;">结论：测试不达标</text>
  <text x="1100" y="730" text-anchor="middle" class="label conclusion-text">所需距离 (16.785 m) > 可用距离 (15.0 m)</text>
  <text x="1100" y="755" text-anchor="middle" class="label conclusion-text">车辆将无法避免碰撞</text>

  <!-- 公式说明 -->
  <text x="900" y="820" class="label" style="font-weight: bold; font-size: 20px;">5. 关键公式说明</text>

  <text x="100" y="860" class="label">速度换算公式：V(m/s) = V(km/h) × (1000/3600)</text>
  <text x="100" y="885" class="label">反应距离公式：D反应 = V₀ × t反应</text>
  <text x="100" y="910" class="label">制动距离公式：D制动 = V₀²/(2a)  （基于运动学方程 v² = v₀² + 2as）</text>
  <text x="100" y="935" class="label">总制动距离：D总 = D反应 + D制动</text>
  <text x="100" y="960" class="label">安全判定：当 D总 ≤ D场景 时，系统能够避免碰撞</text>
</svg>