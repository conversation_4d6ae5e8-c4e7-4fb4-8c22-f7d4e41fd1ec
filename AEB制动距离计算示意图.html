<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AEB制动距离计算示意图</title>
    <!-- MathJax配置 -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js">
    </script>
    <style>
        body {
            font-family: "SimSun", "宋体", serif;
            font-size: 16px;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            width: 1500px;
            height: 800px;
            background-color: white;
            border: 2px solid #333;
            position: relative;
            margin: 0 auto;
        }
        .title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
            color: #2c3e50;
        }
        .section {
            position: absolute;
            border: 2px solid #333;
            border-radius: 10px;
            padding: 15px;
            background-color: #f8f9fa;
        }
        .params {
            top: 80px;
            left: 50px;
            width: 280px;
            height: 180px;
            background-color: #e3f2fd;
            border-color: #1976d2;
        }
        .calculations {
            top: 80px;
            left: 380px;
            width: 650px;
            height: 180px;
            background-color: #fff3e0;
            border-color: #f57c00;
        }
        .formulas {
            top: 80px;
            right: 50px;
            width: 350px;
            height: 180px;
            background-color: #e8f5e8;
            border-color: #388e3c;
        }
        .diagram-section {
            top: 300px;
            left: 50px;
            right: 50px;
            height: 450px;
            background-color: #fafafa;
            border-color: #666;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
            color: #333;
        }
        .param-item, .calc-step, .formula-item {
            margin: 8px 0;
            line-height: 1.4;
        }
        .calc-step {
            margin: 12px 0;
        }
        .step-title {
            font-weight: bold;
            color: #333;
        }
        .step-formula {
            color: #d32f2f;
            font-weight: bold;
            margin-left: 20px;
        }
        .conclusion {
            background-color: #ffebee;
            border: 2px solid #d32f2f;
            border-radius: 8px;
            padding: 10px;
            margin-top: 15px;
            text-align: center;
            font-weight: bold;
            color: #d32f2f;
            font-size: 18px;
        }
        .diagram-title {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }
        .road {
            position: absolute;
            top: 200px;
            left: 100px;
            right: 100px;
            height: 8px;
            background-color: #333;
        }
        .vehicle {
            position: absolute;
            top: 170px;
            left: 120px;
            width: 80px;
            height: 40px;
            background-color: #2196f3;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        .obstacle {
            position: absolute;
            top: 160px;
            right: 200px;
            width: 60px;
            height: 50px;
            background-color: #f44336;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        .distance-arrow {
            position: absolute;
            top: 140px;
            height: 20px;
            border-top: 3px solid #ff9800;
            border-left: 8px solid #ff9800;
            border-right: 8px solid #ff9800;
        }
        .reaction-distance {
            left: 200px;
            width: 150px;
        }
        .braking-distance {
            left: 350px;
            width: 200px;
        }
        .total-distance {
            left: 200px;
            width: 350px;
            border-color: #d32f2f;
            border-width: 4px;
        }
        .distance-label {
            position: absolute;
            top: 115px;
            font-weight: bold;
            text-align: center;
            font-size: 16px;
        }
        .reaction-label {
            left: 200px;
            width: 150px;
            color: #4caf50;
        }
        .braking-label {
            left: 350px;
            width: 200px;
            color: #4caf50;
        }
        .total-label {
            left: 200px;
            width: 350px;
            color: #d32f2f;
            font-size: 18px;
        }
        .scenario-distance {
            position: absolute;
            top: 230px;
            left: 200px;
            right: 200px;
            height: 3px;
            background: repeating-linear-gradient(
                to right,
                #4caf50,
                #4caf50 10px,
                transparent 10px,
                transparent 20px
            );
        }
        .scenario-label {
            position: absolute;
            top: 245px;
            left: 200px;
            right: 200px;
            text-align: center;
            color: #4caf50;
            font-weight: bold;
        }
        .timeline {
            position: absolute;
            top: 300px;
            left: 100px;
            right: 100px;
            height: 3px;
            background-color: #666;
        }
        .timeline-marker {
            position: absolute;
            top: 295px;
            width: 3px;
            height: 13px;
            background-color: #333;
        }
        .timeline-label {
            position: absolute;
            top: 320px;
            font-size: 14px;
            text-align: center;
            width: 100px;
            margin-left: -50px;
        }
        .phase-box {
            position: absolute;
            top: 350px;
            height: 30px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        .reaction-phase {
            left: 200px;
            width: 150px;
            background-color: rgba(255, 152, 0, 0.3);
            border: 2px solid #ff9800;
        }
        .braking-phase {
            left: 350px;
            width: 200px;
            background-color: rgba(244, 67, 54, 0.3);
            border: 2px solid #f44336;
        }
        .speed-label {
            position: absolute;
            top: 120px;
            left: 120px;
            background-color: #fff59d;
            border: 2px solid #fbc02d;
            border-radius: 5px;
            padding: 5px;
            font-size: 14px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">AEB制动距离计算示意图</div>
        
        <!-- 已知参数 -->
        <div class="section params">
            <div class="section-title">已知参数</div>
            <div class="param-item">测试车速 $V_0 = 50$ km/h</div>
            <div class="param-item">系统反应时间 $t = 0.5$ s</div>
            <div class="param-item">最大减速度 $a = 9.8$ m/s²</div>
            <div class="param-item">场景初始距离 $= 15.0$ m</div>
            <div class="param-item" style="font-style: italic; margin-top: 15px;">目标：计算总制动距离</div>
        </div>
        
        <!-- 计算步骤 -->
        <div class="section calculations">
            <div class="section-title">计算步骤</div>
            <div class="calc-step">
                <div class="step-title">步骤一：单位换算</div>
                <div class="step-formula">$$V_0 = 50 \times \frac{1000}{3600} = 13.89 \text{ m/s}$$</div>
            </div>
            <div class="calc-step">
                <div class="step-title">步骤二：计算反应距离</div>
                <div class="step-formula">$$D_{\text{反应}} = V_0 \times t = 13.89 \times 0.5 = 6.945 \text{ m}$$</div>
            </div>
            <div class="calc-step">
                <div class="step-title">步骤三：计算纯制动距离</div>
                <div class="step-formula">$$D_{\text{制动}} = \frac{V_0^2}{2a} = \frac{13.89^2}{2 \times 9.8} = 9.84 \text{ m}$$</div>
            </div>
            <div class="calc-step">
                <div class="step-title">步骤四：计算总制动距离</div>
                <div class="step-formula">$$D_{\text{总}} = D_{\text{反应}} + D_{\text{制动}} = 6.945 + 9.84 = 16.785 \text{ m}$$</div>
            </div>
            <div class="conclusion">结论：$16.785 \text{ m} > 15.0 \text{ m}$，测试不达标！</div>
        </div>
        
        <!-- 关键公式 -->
        <div class="section formulas">
            <div class="section-title">关键公式</div>
            <div class="formula-item"><strong>单位换算：</strong><br>$$V_{(\text{m/s})} = V_{(\text{km/h})} \times \frac{1000}{3600}$$</div>
            <div class="formula-item"><strong>反应距离：</strong><br>$$D_{\text{反应}} = V_0 \times t_{\text{反应}}$$</div>
            <div class="formula-item"><strong>制动距离：</strong><br>$$D_{\text{制动}} = \frac{V_0^2}{2a}$$</div>
        </div>
        
        <!-- 制动过程示意图 -->
        <div class="section diagram-section">
            <div class="diagram-title">制动过程示意图</div>
            
            <!-- 道路 -->
            <div class="road"></div>
            
            <!-- 车辆 -->
            <div class="vehicle">车辆</div>
            <div class="speed-label">$V_0=50$ km/h<br>$(13.89$ m/s$)$</div>
            
            <!-- 障碍物 -->
            <div class="obstacle">障碍物</div>
            
            <!-- 距离箭头和标签 -->
            <div class="distance-arrow reaction-distance"></div>
            <div class="distance-label reaction-label">反应距离<br>6.945 m</div>
            
            <div class="distance-arrow braking-distance"></div>
            <div class="distance-label braking-label">制动距离<br>9.84 m</div>
            
            <div class="distance-arrow total-distance"></div>
            <div class="distance-label total-label">总制动距离 16.785 m</div>
            
            <!-- 场景距离 -->
            <div class="scenario-distance"></div>
            <div class="scenario-label">场景初始距离 15.0 m</div>
            
            <!-- 时间轴 -->
            <div class="timeline"></div>
            <div class="timeline-marker" style="left: 200px;"></div>
            <div class="timeline-marker" style="left: 350px;"></div>
            <div class="timeline-marker" style="left: 550px;"></div>
            
            <div class="timeline-label" style="left: 200px;">t=0<br>发现危险</div>
            <div class="timeline-label" style="left: 350px;">t=0.5s<br>开始制动</div>
            <div class="timeline-label" style="left: 550px;">完全停止</div>
            
            <!-- 阶段标注 -->
            <div class="phase-box reaction-phase">反应阶段</div>
            <div class="phase-box braking-phase">制动阶段</div>
        </div>
    </div>
</body>
</html>
