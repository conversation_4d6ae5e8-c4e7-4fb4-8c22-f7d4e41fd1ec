import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

# 设置中文字体和数学公式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']  # 中文字体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号
plt.rcParams['font.size'] = 16  # 设置字体大小

# 创建图形
fig, ax = plt.subplots(1, 1, figsize=(18.75, 10))  # 1500x800像素，按75dpi计算
ax.set_xlim(0, 100)
ax.set_ylim(0, 100)
ax.axis('off')

# 标题
ax.text(50, 95, 'AEB制动距离计算示意图', fontsize=24, fontweight='bold', 
        ha='center', va='center')

# 已知参数框
param_box = FancyBboxPatch((5, 70), 25, 20, boxstyle="round,pad=0.5", 
                          facecolor='lightblue', edgecolor='navy', linewidth=2)
ax.add_patch(param_box)
ax.text(17.5, 87, '已知参数', fontsize=18, fontweight='bold', ha='center')
ax.text(7, 83, '测试车速 V₀ = 50 km/h', fontsize=16, ha='left')
ax.text(7, 80, '系统反应时间 t = 0.5 s', fontsize=16, ha='left')
ax.text(7, 77, '最大减速度 a = 9.8 m/s²', fontsize=16, ha='left')
ax.text(7, 74, '场景初始距离 = 15.0 m', fontsize=16, ha='left')
ax.text(7, 71, '目标：计算总制动距离', fontsize=16, ha='left', style='italic')

# 计算步骤框
calc_box = FancyBboxPatch((35, 70), 45, 20, boxstyle="round,pad=0.5", 
                         facecolor='lightyellow', edgecolor='orange', linewidth=2)
ax.add_patch(calc_box)
ax.text(57.5, 87, '计算步骤', fontsize=18, fontweight='bold', ha='center')

# 步骤内容
ax.text(37, 84.5, '步骤一：单位换算', fontsize=16, fontweight='bold', ha='left')
ax.text(39, 83, 'V₀ = 50 × (1000/3600) = 13.89 m/s', fontsize=16, ha='left', color='red')

ax.text(37, 81, '步骤二：计算反应距离', fontsize=16, fontweight='bold', ha='left')
ax.text(39, 79.5, 'D反应 = V₀ × t = 13.89 × 0.5 = 6.945 m', fontsize=16, ha='left', color='red')

ax.text(37, 77.5, '步骤三：计算纯制动距离', fontsize=16, fontweight='bold', ha='left')
ax.text(39, 76, 'D制动 = V₀²/(2a) = 13.89²/(2×9.8) = 9.84 m', fontsize=16, ha='left', color='red')

ax.text(37, 74, '步骤四：计算总制动距离', fontsize=16, fontweight='bold', ha='left')
ax.text(39, 72.5, 'D总 = D反应 + D制动 = 6.945 + 9.84 = 16.785 m', fontsize=16, ha='left', color='red')

ax.text(39, 70.5, '结论：16.785 m > 15.0 m，测试不达标！', fontsize=16, ha='left', 
        color='red', fontweight='bold')

# 关键公式框
formula_box = FancyBboxPatch((85, 70), 12, 20, boxstyle="round,pad=0.5", 
                            facecolor='lightgreen', edgecolor='green', linewidth=2)
ax.add_patch(formula_box)
ax.text(91, 87, '关键公式', fontsize=18, fontweight='bold', ha='center')
ax.text(86, 84, '单位换算：', fontsize=16, fontweight='bold', ha='left')
ax.text(86.5, 82.5, 'V(m/s) = V(km/h) × 1000/3600', fontsize=14, ha='left')
ax.text(86, 80.5, '反应距离：', fontsize=16, fontweight='bold', ha='left')
ax.text(86.5, 79, 'D反应 = V₀ × t反应', fontsize=14, ha='left')
ax.text(86, 77, '制动距离：', fontsize=16, fontweight='bold', ha='left')
ax.text(86.5, 75.5, 'D制动 = V₀²/(2a)', fontsize=14, ha='left')

# 制动过程示意图标题
ax.text(50, 62, '制动过程示意图', fontsize=20, fontweight='bold', ha='center')

# 地面线
ax.plot([10, 90], [45, 45], 'k-', linewidth=4)

# 车辆
car = patches.Rectangle((12, 46), 6, 3, linewidth=2, edgecolor='blue', facecolor='lightblue')
ax.add_patch(car)
# 车轮
wheel1 = patches.Circle((13.5, 45.5), 0.8, linewidth=1, edgecolor='blue', facecolor='blue')
wheel2 = patches.Circle((16.5, 45.5), 0.8, linewidth=1, edgecolor='blue', facecolor='blue')
ax.add_patch(wheel1)
ax.add_patch(wheel2)
ax.text(15, 47.5, '车辆', fontsize=14, ha='center', color='white', fontweight='bold')

# 障碍物
obstacle = patches.Rectangle((80, 46), 4, 4, linewidth=2, edgecolor='red', facecolor='lightcoral')
ax.add_patch(obstacle)
ax.text(82, 48, '障碍物', fontsize=14, ha='center', color='white', fontweight='bold')

# 距离标注
# 反应距离
ax.annotate('', xy=(30, 52), xytext=(18, 52), 
            arrowprops=dict(arrowstyle='<->', color='orange', lw=3))
ax.text(24, 54, '反应距离 6.945 m', fontsize=16, ha='center', color='green', fontweight='bold')

# 制动距离
ax.annotate('', xy=(50, 52), xytext=(30, 52), 
            arrowprops=dict(arrowstyle='<->', color='orange', lw=3))
ax.text(40, 54, '制动距离 9.84 m', fontsize=16, ha='center', color='green', fontweight='bold')

# 总距离
ax.annotate('', xy=(50, 56), xytext=(18, 56), 
            arrowprops=dict(arrowstyle='<->', color='red', lw=4))
ax.text(34, 58, '总制动距离 16.785 m', fontsize=18, ha='center', color='red', fontweight='bold')

# 场景距离
ax.plot([18, 80], [42, 42], 'g--', linewidth=3)
ax.text(49, 40, '场景初始距离 15.0 m', fontsize=16, ha='center', color='green')

# 速度标注
ax.text(15, 52, 'V₀=50km/h\n(13.89m/s)', fontsize=14, ha='center', 
        bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.7))

# 时间轴
ax.plot([10, 90], [30, 30], 'gray', linewidth=3)
ax.plot([18, 18], [29, 31], 'k-', linewidth=2)
ax.plot([30, 30], [29, 31], 'k-', linewidth=2)
ax.plot([50, 50], [29, 31], 'k-', linewidth=2)

ax.text(18, 27, 't=0\n发现危险', fontsize=14, ha='center')
ax.text(30, 27, 't=0.5s\n开始制动', fontsize=14, ha='center')
ax.text(50, 27, '完全停止', fontsize=14, ha='center')

# 阶段标注
reaction_phase = patches.Rectangle((18, 32), 12, 3, linewidth=1, 
                                 edgecolor='orange', facecolor='orange', alpha=0.3)
ax.add_patch(reaction_phase)
ax.text(24, 33.5, '反应阶段', fontsize=14, ha='center', fontweight='bold')

braking_phase = patches.Rectangle((30, 32), 20, 3, linewidth=1, 
                                edgecolor='red', facecolor='red', alpha=0.3)
ax.add_patch(braking_phase)
ax.text(40, 33.5, '制动阶段', fontsize=14, ha='center', fontweight='bold')

# 保存图片
plt.tight_layout()
plt.savefig('AEB制动距离计算示意图.png', dpi=100, bbox_inches='tight',
            facecolor='white', edgecolor='none')
plt.savefig('AEB制动距离计算示意图.pdf', bbox_inches='tight',
            facecolor='white', edgecolor='none')
# 不显示图片，避免阻塞
# plt.show()

print("图表已生成完成！")
print("已保存为PNG和PDF两种格式")
