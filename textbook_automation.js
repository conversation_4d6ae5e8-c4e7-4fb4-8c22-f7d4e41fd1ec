const { chromium } = require('playwright');

async function automateTextbookEditor() {
    console.log('启动浏览器...');
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000 // 减慢操作速度以便观察
    });
    
    const context = await browser.newContext({
        viewport: { width: 1920, height: 1080 }
    });
    
    const page = await context.newPage();
    
    try {
        console.log('导航到登录页面...');
        await page.goto('https://textbookeditor.lessonplan.cn/ed0b4d38-03c8-150b-edab-a4d423944978');
        
        // 等待页面加载
        await page.waitForTimeout(3000);
        
        console.log('查找登录表单...');
        // 尝试多种可能的登录表单选择器
        const loginSelectors = [
            'input[type="text"]',
            'input[name="username"]',
            'input[name="phone"]',
            'input[placeholder*="手机"]',
            'input[placeholder*="用户名"]',
            '.login-input',
            '#username',
            '#phone'
        ];
        
        let usernameInput = null;
        for (const selector of loginSelectors) {
            try {
                usernameInput = await page.waitForSelector(selector, { timeout: 2000 });
                if (usernameInput) {
                    console.log(`找到用户名输入框: ${selector}`);
                    break;
                }
            } catch (e) {
                continue;
            }
        }
        
        if (!usernameInput) {
            console.log('未找到用户名输入框，尝试截图查看页面状态...');
            await page.screenshot({ path: 'login_page.png', fullPage: true });
            throw new Error('无法找到用户名输入框');
        }
        
        console.log('输入用户名...');
        await usernameInput.fill('13609281701');
        
        // 查找密码输入框
        const passwordSelectors = [
            'input[type="password"]',
            'input[name="password"]',
            'input[placeholder*="密码"]',
            '.password-input',
            '#password'
        ];
        
        let passwordInput = null;
        for (const selector of passwordSelectors) {
            try {
                passwordInput = await page.waitForSelector(selector, { timeout: 2000 });
                if (passwordInput) {
                    console.log(`找到密码输入框: ${selector}`);
                    break;
                }
            } catch (e) {
                continue;
            }
        }
        
        if (!passwordInput) {
            throw new Error('无法找到密码输入框');
        }
        
        console.log('输入密码...');
        await passwordInput.fill('swy2011040052');
        
        // 查找登录按钮
        const loginButtonSelectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            'button:has-text("登录")',
            'button:has-text("登陆")',
            'button:has-text("Login")',
            '.login-btn',
            '.submit-btn',
            '#login-btn'
        ];
        
        let loginButton = null;
        for (const selector of loginButtonSelectors) {
            try {
                loginButton = await page.waitForSelector(selector, { timeout: 2000 });
                if (loginButton) {
                    console.log(`找到登录按钮: ${selector}`);
                    break;
                }
            } catch (e) {
                continue;
            }
        }
        
        if (!loginButton) {
            // 尝试按回车键登录
            console.log('未找到登录按钮，尝试按回车键...');
            await passwordInput.press('Enter');
        } else {
            console.log('点击登录按钮...');
            await loginButton.click();
        }
        
        // 等待登录完成
        console.log('等待登录完成...');
        await page.waitForTimeout(5000);
        
        // 检查是否登录成功
        const currentUrl = page.url();
        console.log(`当前URL: ${currentUrl}`);
        
        // 截图查看当前页面状态
        await page.screenshot({ path: 'after_login.png', fullPage: true });
        
        console.log('查找1.1节内容...');
        // 查找1.1节的内容
        const section11Selectors = [
            'text=1.1',
            '[data-section="1.1"]',
            '.section-1-1',
            'h1:has-text("1.1")',
            'h2:has-text("1.1")',
            'h3:has-text("1.1")',
            'div:has-text("1.1")'
        ];
        
        let section11Element = null;
        for (const selector of section11Selectors) {
            try {
                section11Element = await page.waitForSelector(selector, { timeout: 3000 });
                if (section11Element) {
                    console.log(`找到1.1节: ${selector}`);
                    break;
                }
            } catch (e) {
                continue;
            }
        }
        
        if (!section11Element) {
            console.log('未找到1.1节，尝试查找所有可能的内容区域...');
            // 截图帮助调试
            await page.screenshot({ path: 'content_page.png', fullPage: true });
            
            // 尝试查找编辑器区域
            const editorSelectors = [
                '.editor',
                '.content-editor',
                '.text-editor',
                '[contenteditable="true"]',
                'iframe',
                '.ql-editor'
            ];
            
            for (const selector of editorSelectors) {
                try {
                    const element = await page.waitForSelector(selector, { timeout: 2000 });
                    if (element) {
                        console.log(`找到编辑器区域: ${selector}`);
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }
        }
        
        console.log('脚本执行完成，浏览器将保持打开状态以便手动操作...');
        console.log('请手动完成以下操作：');
        console.log('1. 找到1.1节的内容');
        console.log('2. 选择整个1.1节的内容块');
        console.log('3. 复制该内容块');
        console.log('4. 粘贴并修改为1.2节的内容');
        console.log('5. 使用Ctrl+M渲染公式');
        console.log('6. 重复此过程创建其他节');
        
        // 保持浏览器打开
        await page.waitForTimeout(300000); // 等待5分钟
        
    } catch (error) {
        console.error('发生错误:', error);
        await page.screenshot({ path: 'error_screenshot.png', fullPage: true });
    } finally {
        // 不关闭浏览器，让用户手动操作
        console.log('浏览器保持打开状态...');
    }
}

// 运行自动化脚本
automateTextbookEditor().catch(console.error);
