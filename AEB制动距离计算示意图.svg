<?xml version="1.0" encoding="UTF-8"?>
<svg width="1500" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: SimSun, serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .text { font-family: SimSun, serif; font-size: 16px; fill: #2c3e50; }
      .formula { font-family: SimSun, serif; font-size: 16px; fill: #e74c3c; font-weight: bold; }
      .result { font-family: SimSun, serif; font-size: 16px; fill: #27ae60; font-weight: bold; }
      .warning { font-family: SimSun, serif; font-size: 18px; fill: #e74c3c; font-weight: bold; }
      .car { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .obstacle { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .distance-line { stroke: #f39c12; stroke-width: 3; }
      .arrow { fill: #f39c12; }
    </style>
  </defs>
  
  <!-- 标题 -->
  <text x="750" y="40" text-anchor="middle" class="title">AEB制动距离计算示意图</text>
  
  <!-- 已知参数 -->
  <rect x="50" y="70" width="300" height="180" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="200" y="95" text-anchor="middle" class="text" font-weight="bold">已知参数</text>
  <text x="70" y="120" class="text">测试车速 V₀ = 50 km/h</text>
  <text x="70" y="145" class="text">系统反应时间 t = 0.5 s</text>
  <text x="70" y="170" class="text">最大减速度 a = 9.8 m/s²</text>
  <text x="70" y="195" class="text">场景初始距离 = 15.0 m</text>
  <text x="70" y="220" class="text">目标：计算总制动距离</text>
  
  <!-- 计算步骤 -->
  <rect x="400" y="70" width="650" height="300" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="725" y="95" text-anchor="middle" class="text" font-weight="bold">计算步骤</text>
  
  <!-- 步骤1 -->
  <text x="420" y="125" class="text">步骤一：单位换算</text>
  <text x="440" y="150" class="formula">V₀ = 50 × (1000/3600) = 13.89 m/s</text>
  
  <!-- 步骤2 -->
  <text x="420" y="180" class="text">步骤二：计算反应距离</text>
  <text x="440" y="205" class="formula">D反应 = V₀ × t = 13.89 × 0.5 = 6.945 m</text>
  
  <!-- 步骤3 -->
  <text x="420" y="235" class="text">步骤三：计算纯制动距离</text>
  <text x="440" y="260" class="formula">D制动 = V₀²/(2a) = 13.89²/(2×9.8) = 9.84 m</text>
  
  <!-- 步骤4 -->
  <text x="420" y="290" class="text">步骤四：计算总制动距离</text>
  <text x="440" y="315" class="formula">D总 = D反应 + D制动 = 6.945 + 9.84 = 16.785 m</text>
  
  <!-- 结论 -->
  <text x="440" y="345" class="warning">结论：16.785 m > 15.0 m，测试不达标！</text>
  
  <!-- 示意图部分 -->
  <text x="750" y="420" text-anchor="middle" class="text" font-weight="bold">制动过程示意图</text>
  
  <!-- 地面线 -->
  <line x1="100" y1="500" x2="1400" y2="500" stroke="#34495e" stroke-width="3"/>
  
  <!-- 车辆 -->
  <rect x="120" y="460" width="80" height="40" class="car" rx="5"/>
  <circle cx="140" cy="510" r="15" class="car"/>
  <circle cx="180" cy="510" r="15" class="car"/>
  <text x="160" y="485" text-anchor="middle" class="text" fill="white" font-size="14">车辆</text>
  
  <!-- 障碍物 -->
  <rect x="1250" y="450" width="60" height="50" class="obstacle" rx="5"/>
  <text x="1280" y="480" text-anchor="middle" class="text" fill="white" font-size="14">障碍物</text>
  
  <!-- 距离标注 -->
  <!-- 反应距离 -->
  <line x1="200" y1="450" x2="350" y2="450" class="distance-line"/>
  <polygon points="200,445 210,450 200,455" class="arrow"/>
  <polygon points="350,445 340,450 350,455" class="arrow"/>
  <text x="275" y="440" text-anchor="middle" class="result">反应距离 6.945 m</text>
  
  <!-- 制动距离 -->
  <line x1="350" y1="430" x2="550" y2="430" class="distance-line"/>
  <polygon points="350,425 360,430 350,435" class="arrow"/>
  <polygon points="550,425 540,430 550,435" class="arrow"/>
  <text x="450" y="420" text-anchor="middle" class="result">制动距离 9.84 m</text>
  
  <!-- 总距离 -->
  <line x1="200" y1="410" x2="550" y2="410" stroke="#e74c3c" stroke-width="4"/>
  <polygon points="200,405 210,410 200,415" fill="#e74c3c"/>
  <polygon points="550,405 540,410 550,415" fill="#e74c3c"/>
  <text x="375" y="400" text-anchor="middle" class="warning">总制动距离 16.785 m</text>
  
  <!-- 场景距离 -->
  <line x1="200" y1="530" x2="1250" y2="530" stroke="#27ae60" stroke-width="3" stroke-dasharray="10,5"/>
  <text x="725" y="550" text-anchor="middle" class="text">场景初始距离 15.0 m</text>
  
  <!-- 速度标注 -->
  <text x="160" y="440" text-anchor="middle" class="text">V₀=50km/h</text>
  <text x="160" y="425" text-anchor="middle" class="text">(13.89m/s)</text>
  
  <!-- 时间轴 -->
  <line x1="100" y1="600" x2="1400" y2="600" stroke="#7f8c8d" stroke-width="2"/>
  <text x="200" y="620" text-anchor="middle" class="text">t=0</text>
  <text x="350" y="620" text-anchor="middle" class="text">t=0.5s</text>
  <text x="550" y="620" text-anchor="middle" class="text">完全停止</text>
  <text x="200" y="640" text-anchor="middle" class="text">发现危险</text>
  <text x="350" y="640" text-anchor="middle" class="text">开始制动</text>
  
  <!-- 阶段标注 -->
  <rect x="220" y="660" width="110" height="30" fill="#f39c12" opacity="0.3" rx="5"/>
  <text x="275" y="680" text-anchor="middle" class="text">反应阶段</text>
  
  <rect x="370" y="660" width="110" height="30" fill="#e74c3c" opacity="0.3" rx="5"/>
  <text x="425" y="680" text-anchor="middle" class="text">制动阶段</text>
  
  <!-- 公式总结 -->
  <rect x="1100" y="70" width="350" height="200" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="1275" y="95" text-anchor="middle" class="text" font-weight="bold">关键公式</text>
  <text x="1120" y="125" class="formula">单位换算：</text>
  <text x="1140" y="145" class="text">V(m/s) = V(km/h) × 1000/3600</text>
  <text x="1120" y="175" class="formula">反应距离：</text>
  <text x="1140" y="195" class="text">D反应 = V₀ × t反应</text>
  <text x="1120" y="225" class="formula">制动距离：</text>
  <text x="1140" y="245" class="text">D制动 = V₀²/(2a)</text>
</svg>
