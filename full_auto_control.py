import asyncio
from playwright.async_api import async_playwright

class FullAutoControl:
    def __init__(self):
        self.browser = None
        self.context = None
        self.page = None
        
    async def start_and_login(self):
        """启动浏览器并自动登录"""
        print('🚀 启动浏览器并自动登录...')
        
        async with async_playwright() as p:
            self.browser = await p.chromium.launch(
                headless=False, 
                slow_mo=1000,
                args=['--start-maximized']  # 最大化窗口
            )
            
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            
            self.page = await self.context.new_page()
            
            # 导航到页面
            print('📱 导航到教材编辑器...')
            await self.page.goto('https://textbookeditor.lessonplan.cn/ed0b4d38-03c8-150b-edab-a4d423944978')
            await self.page.wait_for_timeout(3000)
            
            # 自动登录
            await self.auto_login()
            
            # 等待页面完全加载
            await self.page.wait_for_timeout(5000)
            
            # 查看所有内容
            await self.explore_all_content()
            
            # 执行复制操作
            await self.execute_section_copying()
    
    async def auto_login(self):
        """自动登录"""
        print('🔐 开始自动登录...')
        
        try:
            # 查找用户名输入框
            username_selectors = [
                'input[type="text"]',
                'input[name="username"]',
                'input[name="phone"]',
                'input[placeholder*="手机"]',
                'input[placeholder*="用户名"]'
            ]
            
            username_input = None
            for selector in username_selectors:
                try:
                    username_input = await self.page.wait_for_selector(selector, timeout=3000)
                    if username_input:
                        print(f'✅ 找到用户名输入框: {selector}')
                        break
                except:
                    continue
            
            if username_input:
                await username_input.fill('13609281701')
                print('✅ 已输入用户名')
            
            # 查找密码输入框
            password_input = await self.page.wait_for_selector('input[type="password"]', timeout=5000)
            if password_input:
                await password_input.fill('swy2011040052')
                print('✅ 已输入密码')
            
            # 查找并点击正确的登录按钮（避免微信登录）
            login_selectors = [
                'button[type="submit"]:not(:has-text("微信"))',
                'input[type="submit"]',
                'button.login-btn:not(:has-text("微信"))',
                'form button:not(:has-text("微信"))'
            ]
            
            login_success = False
            for selector in login_selectors:
                try:
                    login_button = await self.page.wait_for_selector(selector, timeout=2000)
                    if login_button:
                        button_text = await login_button.text_content()
                        if '微信' not in (button_text or ''):
                            await login_button.click()
                            print(f'✅ 点击登录按钮: {selector}')
                            login_success = True
                            break
                except:
                    continue
            
            if not login_success:
                # 尝试按回车键登录
                print('🔍 尝试按回车键登录...')
                await password_input.press('Enter')
            
            # 等待登录完成
            await self.page.wait_for_timeout(5000)
            
            current_url = self.page.url
            print(f'🌐 登录后URL: {current_url}')
            
            if 'passport' not in current_url:
                print('✅ 登录成功！')
            else:
                print('⚠️ 可能需要额外验证')
                
        except Exception as e:
            print(f'❌ 登录过程中出错: {e}')
    
    async def explore_all_content(self):
        """探索并查看所有页面内容"""
        print('🔍 探索页面内容...')
        
        # 截图当前状态
        await self.page.screenshot(path='full_page_view.png', full_page=True)
        print('📸 已保存完整页面截图: full_page_view.png')
        
        # 获取页面标题
        title = await self.page.title()
        print(f'📄 页面标题: {title}')
        
        # 查找所有iframe
        iframes = await self.page.query_selector_all('iframe')
        print(f'🔍 找到 {len(iframes)} 个iframe')
        
        # 检查每个iframe的内容
        for i, iframe in enumerate(iframes):
            try:
                frame = await iframe.content_frame()
                if frame:
                    print(f'🔍 检查第 {i+1} 个iframe...')
                    
                    # 等待iframe加载
                    await frame.wait_for_timeout(2000)
                    
                    # 获取iframe内容
                    frame_content = await frame.content()
                    if '1.1' in frame_content:
                        print(f'✅ 第 {i+1} 个iframe包含1.1内容')
                        
                        # 截图iframe内容
                        await frame.screenshot(path=f'iframe_{i+1}_content.png')
                        print(f'📸 已保存第 {i+1} 个iframe截图')
                        
                        # 查找1.1相关元素
                        elements_with_11 = await frame.query_selector_all('*')
                        for element in elements_with_11:
                            try:
                                text = await element.text_content()
                                if text and '1.1' in text and len(text.strip()) > 5:
                                    print(f'📝 找到1.1内容: {text[:100]}...')
                            except:
                                continue
            except Exception as e:
                print(f'❌ 检查第 {i+1} 个iframe时出错: {e}')
        
        # 在主页面查找内容
        print('🔍 在主页面查找1.1内容...')
        all_elements = await self.page.query_selector_all('*')
        found_11_elements = []
        
        for element in all_elements:
            try:
                text = await element.text_content()
                if text and '1.1' in text and len(text.strip()) > 5:
                    found_11_elements.append((element, text))
                    print(f'📝 主页面找到1.1内容: {text[:80]}...')
            except:
                continue
        
        print(f'✅ 总共在主页面找到 {len(found_11_elements)} 个包含1.1的元素')
        
        return found_11_elements, iframes
    
    async def execute_section_copying(self):
        """执行节复制操作"""
        print('📋 开始执行节复制操作...')
        
        # 探索内容
        main_elements, iframes = await self.explore_all_content()
        
        # 尝试在iframe中操作
        for i, iframe in enumerate(iframes):
            try:
                frame = await iframe.content_frame()
                if frame:
                    print(f'🔄 尝试在第 {i+1} 个iframe中操作...')
                    
                    # 查找1.1节
                    elements_with_11 = await frame.query_selector_all('*')
                    section_element = None
                    
                    for element in elements_with_11:
                        try:
                            text = await element.text_content()
                            if text and '1.1' in text and len(text.strip()) > 20:  # 找到较长的内容
                                section_element = element
                                print(f'🎯 选择元素进行复制: {text[:50]}...')
                                break
                        except:
                            continue
                    
                    if section_element:
                        success = await self.copy_and_create_sections_in_frame(frame, section_element)
                        if success:
                            print('✅ 在iframe中成功完成操作')
                            return True
            except Exception as e:
                print(f'❌ 在第 {i+1} 个iframe中操作失败: {e}')
                continue
        
        # 如果iframe中没有成功，尝试在主页面操作
        if main_elements:
            print('🔄 尝试在主页面操作...')
            element, text = main_elements[0]  # 选择第一个元素
            success = await self.copy_and_create_sections_in_frame(self.page, element)
            if success:
                print('✅ 在主页面成功完成操作')
                return True
        
        print('❌ 所有尝试都失败了')
        return False
    
    async def copy_and_create_sections_in_frame(self, page_or_frame, section_element):
        """在指定的页面或frame中复制并创建节"""
        try:
            print('📋 开始复制1.1节完整内容...')
            
            # 点击元素
            await section_element.click()
            await page_or_frame.wait_for_timeout(1000)
            
            # 全选当前内容
            await page_or_frame.keyboard.press('Control+A')
            await page_or_frame.wait_for_timeout(500)
            
            # 复制
            await page_or_frame.keyboard.press('Control+C')
            await page_or_frame.wait_for_timeout(1000)
            print('✅ 已复制内容')
            
            # 创建多个节
            sections_to_create = [2, 3, 4, 5, 6, 7, 8, 9, 10]  # 创建1.2到1.10
            
            for section_num in sections_to_create:
                print(f'🔄 创建1.{section_num}节...')
                
                # 移动到文档末尾
                await page_or_frame.keyboard.press('Control+End')
                await page_or_frame.keyboard.press('Enter')
                await page_or_frame.keyboard.press('Enter')
                await page_or_frame.wait_for_timeout(500)
                
                # 粘贴内容
                await page_or_frame.keyboard.press('Control+V')
                await page_or_frame.wait_for_timeout(2000)
                
                # 使用查找替换功能修改节号
                await page_or_frame.keyboard.press('Control+H')  # 打开查找替换
                await page_or_frame.wait_for_timeout(1000)
                
                # 输入查找内容
                await page_or_frame.keyboard.type('1.1')
                await page_or_frame.keyboard.press('Tab')  # 移动到替换框
                
                # 输入替换内容
                await page_or_frame.keyboard.type(f'1.{section_num}')
                
                # 执行替换（只替换一次，替换最新粘贴的）
                await page_or_frame.keyboard.press('Alt+R')  # 替换
                await page_or_frame.wait_for_timeout(500)
                
                # 关闭查找替换对话框
                await page_or_frame.keyboard.press('Escape')
                await page_or_frame.wait_for_timeout(500)
                
                print(f'✅ 成功创建1.{section_num}节')
            
            # 最终截图
            if hasattr(page_or_frame, 'screenshot'):
                await page_or_frame.screenshot(path='final_result.png', full_page=True)
            else:
                await self.page.screenshot(path='final_result.png', full_page=True)
            
            print('📸 已保存最终结果截图: final_result.png')
            print('🎉 所有节创建完成！')
            print('💡 请使用 Ctrl+M 渲染公式')
            
            return True
            
        except Exception as e:
            print(f'❌ 复制创建过程中出错: {e}')
            return False

# 运行完整自动化
async def main():
    controller = FullAutoControl()
    await controller.start_and_login()
    
    # 保持浏览器打开
    print('🌐 浏览器保持打开状态，您可以查看结果...')
    await asyncio.sleep(300)  # 等待5分钟

if __name__ == "__main__":
    asyncio.run(main())
