import asyncio
import time
from playwright.async_api import async_playwright

async def automate_textbook_editor():
    print('启动浏览器...')
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(
            headless=False,
            slow_mo=1000  # 减慢操作速度以便观察
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        
        page = await context.new_page()
        
        try:
            print('导航到登录页面...')
            await page.goto('https://textbookeditor.lessonplan.cn/ed0b4d38-03c8-150b-edab-a4d423944978')
            
            # 等待页面加载
            await page.wait_for_timeout(3000)
            
            print('查找登录表单...')
            # 尝试多种可能的登录表单选择器
            login_selectors = [
                'input[type="text"]',
                'input[name="username"]',
                'input[name="phone"]',
                'input[placeholder*="手机"]',
                'input[placeholder*="用户名"]',
                '.login-input',
                '#username',
                '#phone'
            ]
            
            username_input = None
            for selector in login_selectors:
                try:
                    username_input = await page.wait_for_selector(selector, timeout=2000)
                    if username_input:
                        print(f'找到用户名输入框: {selector}')
                        break
                except Exception:
                    continue
            
            if not username_input:
                print('未找到用户名输入框，尝试截图查看页面状态...')
                await page.screenshot(path='login_page.png', full_page=True)
                raise Exception('无法找到用户名输入框')
            
            print('输入用户名...')
            await username_input.fill('13609281701')
            
            # 查找密码输入框
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input[placeholder*="密码"]',
                '.password-input',
                '#password'
            ]
            
            password_input = None
            for selector in password_selectors:
                try:
                    password_input = await page.wait_for_selector(selector, timeout=2000)
                    if password_input:
                        print(f'找到密码输入框: {selector}')
                        break
                except Exception:
                    continue
            
            if not password_input:
                raise Exception('无法找到密码输入框')
            
            print('输入密码...')
            await password_input.fill('swy2011040052')
            
            # 查找登录按钮
            login_button_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("登录")',
                'button:has-text("登陆")',
                'button:has-text("Login")',
                '.login-btn',
                '.submit-btn',
                '#login-btn'
            ]
            
            login_button = None
            for selector in login_button_selectors:
                try:
                    login_button = await page.wait_for_selector(selector, timeout=2000)
                    if login_button:
                        print(f'找到登录按钮: {selector}')
                        break
                except Exception:
                    continue
            
            if not login_button:
                # 尝试按回车键登录
                print('未找到登录按钮，尝试按回车键...')
                await password_input.press('Enter')
            else:
                print('点击登录按钮...')
                await login_button.click()
            
            # 等待登录完成
            print('等待登录完成...')
            await page.wait_for_timeout(5000)
            
            # 检查是否登录成功
            current_url = page.url
            print(f'当前URL: {current_url}')
            
            # 截图查看当前页面状态
            await page.screenshot(path='after_login.png', full_page=True)
            
            print('查找1.1节内容...')
            # 查找1.1节的内容
            section_11_selectors = [
                'text=1.1',
                '[data-section="1.1"]',
                '.section-1-1',
                'h1:has-text("1.1")',
                'h2:has-text("1.1")',
                'h3:has-text("1.1")',
                'div:has-text("1.1")'
            ]
            
            section_11_element = None
            for selector in section_11_selectors:
                try:
                    section_11_element = await page.wait_for_selector(selector, timeout=3000)
                    if section_11_element:
                        print(f'找到1.1节: {selector}')
                        break
                except Exception:
                    continue
            
            if not section_11_element:
                print('未找到1.1节，尝试查找所有可能的内容区域...')
                # 截图帮助调试
                await page.screenshot(path='content_page.png', full_page=True)
                
                # 尝试查找编辑器区域
                editor_selectors = [
                    '.editor',
                    '.content-editor',
                    '.text-editor',
                    '[contenteditable="true"]',
                    'iframe',
                    '.ql-editor'
                ]

                iframe_element = None
                for selector in editor_selectors:
                    try:
                        element = await page.wait_for_selector(selector, timeout=2000)
                        if element:
                            print(f'找到编辑器区域: {selector}')
                            if selector == 'iframe':
                                iframe_element = element
                            break
                    except Exception:
                        continue

                # 如果找到iframe，尝试在iframe内查找1.1节
                if iframe_element:
                    print('尝试进入iframe查找内容...')
                    try:
                        # 获取iframe的frame对象
                        frame = await iframe_element.content_frame()
                        if frame:
                            print('成功进入iframe')

                            # 在iframe内查找1.1节
                            for selector in section_11_selectors:
                                try:
                                    section_11_element = await frame.wait_for_selector(selector, timeout=3000)
                                    if section_11_element:
                                        print(f'在iframe中找到1.1节: {selector}')

                                        # 在iframe中执行复制操作
                                        await self.copy_section_in_frame(frame, section_11_element)
                                        break
                                except Exception:
                                    continue
                    except Exception as e:
                        print(f'处理iframe时出错: {e}')
            
            # 尝试自动化复制1.1节并创建1.2节
            if section_11_element:
                print('尝试复制1.1节内容...')

                # 获取1.1节的父容器
                parent_element = await section_11_element.evaluate('element => element.parentElement')

                # 点击1.1节元素
                await section_11_element.click()
                await page.wait_for_timeout(1000)

                # 尝试选择整个内容块
                await page.keyboard.press('Control+A')  # 全选
                await page.wait_for_timeout(500)
                await page.keyboard.press('Control+C')  # 复制
                await page.wait_for_timeout(500)

                print('尝试粘贴并修改为1.2节...')

                # 移动到内容末尾
                await page.keyboard.press('Control+End')  # 移动到文档末尾
                await page.keyboard.press('Enter')  # 换行
                await page.keyboard.press('Enter')  # 再换行
                await page.wait_for_timeout(500)

                # 粘贴内容
                await page.keyboard.press('Control+V')  # 粘贴
                await page.wait_for_timeout(1000)

                # 尝试查找并修改新粘贴的1.1为1.2
                try:
                    # 查找页面中的所有1.1文本
                    elements_with_11 = await page.query_selector_all('text=1.1')
                    if len(elements_with_11) > 1:  # 如果有多个1.1，说明复制成功了
                        last_11_element = elements_with_11[-1]  # 获取最后一个（新复制的）
                        await last_11_element.click()
                        await page.wait_for_timeout(500)

                        # 选择1.1文本并替换为1.2
                        await page.keyboard.press('Control+A')
                        await page.keyboard.type('1.2')
                        await page.wait_for_timeout(500)

                        print('成功创建1.2节！')
                    else:
                        print('复制可能未成功，请手动操作')
                except Exception as e:
                    print(f'修改标题时出错: {e}')

                print('内容已复制并修改，请手动调整内容并使用Ctrl+M渲染公式')
            
            print('脚本执行完成，浏览器将保持打开状态以便手动操作...')
            print('请手动完成以下操作：')
            print('1. 检查复制的内容是否正确')
            print('2. 修改1.2节的具体内容')
            print('3. 使用Ctrl+M渲染公式')
            print('4. 重复此过程创建其他节')
            
            # 保持浏览器打开
            await page.wait_for_timeout(300000)  # 等待5分钟
            
        except Exception as error:
            print(f'发生错误: {error}')
            await page.screenshot(path='error_screenshot.png', full_page=True)
        finally:
            # 不关闭浏览器，让用户手动操作
            print('浏览器保持打开状态...')

# 运行自动化脚本
if __name__ == "__main__":
    asyncio.run(automate_textbook_editor())
